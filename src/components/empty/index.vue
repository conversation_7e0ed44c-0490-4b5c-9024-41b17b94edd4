<template>
  <n-empty :class="$style['empty']" :theme-overrides="overrideEmptyTheme()" :description="title" />
</template>

<script lang="ts" setup>
import { overrideEmptyTheme } from './emptyTheme';
const props = defineProps({
  title: {
    type: String,
    default: '暂无数据',
  },
});

defineOptions({ name: 'ComEmpty' });
</script>

<style module lang="scss">
.empty {
  @apply h-full justify-center;
}
</style>
