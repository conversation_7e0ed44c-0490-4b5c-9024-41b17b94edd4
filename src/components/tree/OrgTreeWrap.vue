<template>
  <div :class="[$style.ComOrgTreeWrap, isNarrow ? $style.isNarrow : '']">
    <div :class="$style.treeContainer">
      <n-spin class="h-full" content-class="h-full" :show="loading">
        <ComHeaderC title="所属单位" class="ml-[20px] mt-[10px]" />
        <OrgTree class="w-[323px] p-[20px]" :data="treeData" @action="handleChange" />
      </n-spin>
    </div>

    <img :class="$style.expand" :src="expandImg" alt="" @click="setNarrow(!isNarrow)" />
  </div>
</template>

<script setup lang="ts">
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import expandImg from '@/assets/strenth.png';
import OrgTree from './index.vue';
import { computed, nextTick, ref } from 'vue';
import { postTreeList } from '@/views/common/fetchData';
import { TreeOption } from 'naive-ui';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useState } from '@/common/hooks/useState.ts';
import { useStore } from '@/store';

const props = defineProps({
  // 自定义参数
  customParams: {
    type: Object,
    default: () => {},
  },
});
const emits = defineEmits(['change']);

const [loading, run] = useAutoLoading(false);
const treeData = ref<TreeOption[]>([]);
const store = useStore();
// 是否有多个组织机构　多个时展开，否则收起
const isMutUnitOrg = computed(() => store.userInfo.unitOrgType == '2');
const [isNarrow, setNarrow] = useState(!isMutUnitOrg.value);

function handleChange(val: TreeOption) {
  console.log(val, '-=-=-===-=-=handleChange');
  emits('change', val);
}

function getData() {
  const params = {
    needChildUnit: '1', // 不要下级单位,1要，0不要
    needself: '1', // 是否包含自己,1包含 0不包含
    orgCode: store.userInfo.orgCode, // 机构id=10000,顶级是-1
    ...props.customParams,
  };

  run(postTreeList(params)).then((res) => {
    const data = res.data || [];
    treeData.value = data;

    // 默认派发根节点
    // if (data.length) {
    //   emits('change', data[0]);
    // }
  });
}

// init
nextTick(() => {
  getData();
});

defineOptions({ name: 'ComOrgTreeWrap' });
</script>

<style module lang="scss">
.ComOrgTreeWrap {
  position: relative;
  height: 100%;
  margin-right: 20px;
  transition: margin-right 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  .treeContainer {
    width: 323px;
    height: 100%;
    transition:
      width 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    border: 1px solid #4a5c7e;
    background: rgba(var(--com-container-bg), 0.2);
  }

  .expand {
    position: absolute;
    top: 45.5%;
    right: -36px;
    width: 32px;
    height: 32px;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 999;
    transition:
      right 0.4s cubic-bezier(0.4, 0, 0.2, 1),
      transform 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
  }

  &.isNarrow {
    position: relative;
    margin-right: -32px;

    .treeContainer {
      width: 0;
      opacity: 0;
    }

    .expand {
      right: 4px;
      transform: translate(-50%, -50%) rotate(180deg);
    }
  }

  :global(.n-empty) {
    padding-top: 50%;
  }
}
</style>
