<template>
  <div :class="$style['equipmentListWrap']">
    <template v-if="equipmentList.length >= 1">
      <div :class="$style['equipmentList']" v-for="(item, index) in equipmentList" :key="index">
        <div :class="$style['header']">
          <p>{{ treeName }}</p>
          <n-button @click="goDetail(item)" width="60" height="32" color="#527CFF" text-color="white" type="info">
            更多
          </n-button>
        </div>
        <div :class="$style['content']">
          <div :class="$style['room']">
            <div :class="$style['roomHeader']">
              <p>{{ item.hiveId ? '机库' : '无人机' }}</p>
              <!-- <n-image width="84" height="76" src="https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg" /> -->
              <img v-if="item.hiveId" :class="$style['robot__image']" :src="imgBaseUrl + item.hivePicture" />
              <img v-else :class="$style['robot__image']" :src="imgBaseUrl + item.uavPicture" />
              <!-- <img width="84" height="76" src="@/assets/polling/_house.png" v-if="item.id === '2'" /> -->
            </div>
            <div :class="$style['info']">
              <p></p>
              <div>
                设备名称：<span :class="$style['eqName']" :title="item.uavName">{{
                  item.hiveId ? item.hiveName : item.uavName
                }}</span>
              </div>
              <div>
                设备型号：<span :class="$style['eqType']" :title="item.model">{{
                  item.hiveId ? item.hiveModel : item.model
                }}</span>
              </div>
              <div>
                设备编号：<span :class="$style['eqNum']" :title="item.uavId">{{
                  item.hiveId ? item.hiveId : item.uavId
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div :class="$style['empty__box']" v-else>
      <Empty title="" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { PropType, ref, defineEmits, defineProps } from 'vue';
import type { ICardBItem } from './type';
import Empty from '@/components/empty/index.vue';

const imgBaseUrl = window.$SYS_CFG.fileService;

const emits = defineEmits(['gotoDetail']);
const props = defineProps({
  equipmentList: {
    type: Array as PropType<ICardBItem[]>,
    default: () => [],
  },
  colors: {
    type: Array as PropType<string[]>,
    default: () => ['#F18D00', '#527CFF'],
  },
  treeName: {
    type: String,
    default: '',
  },
});
function goDetail(obj: any) {
  emits('gotoDetail', obj);
}
</script>

<style module lang="scss">
.equipmentListWrap {
  border-radius: 4px;
  display: flex;
  flex-wrap: wrap;
  padding-left: 10px;
  .equipmentList {
    width: 30.5%;
    height: 210px;
    background: white;
    border: 1px dashed #ccc;
    margin: 10px;
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border-bottom: 1px dashed #ccc;
      p {
        font-weight: 700;
        font-size: 16px;
        color: #222222;
      }
    }

    .content {
      display: flex;
      justify-content: flex-start;
      .room {
        display: flex;
        width: 100%;
        padding: 16px 17px 17px 12px;
        .roomHeader {
          p {
            font-weight: bolder;
            font-size: 16px;
            color: #222222;
            margin-bottom: 22px;
          }
          .robot__image {
            width: 86px !important;
          }
        }
        .info {
          color: #222222;
          margin-left: 12px;
          p {
            font-weight: bolder;
            color: white;
            padding: 2px 6px;
            margin-bottom: 22px;
            border-radius: 4px;
            // width: 60px;
            display: inline-block;
          }
          div {
            margin-bottom: 12px;
            display: flex;
            white-space: nowrap;
          }
          .eqName {
            font-weight: 400;
            display: inline-block;
            width: 79px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .eqNum {
            width: 79px;
            font-weight: 400;
          }
          .eqType {
            font-weight: 400;
            display: inline-block;
            width: 79px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
  .empty__box {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 500px;
  }
}
</style>
