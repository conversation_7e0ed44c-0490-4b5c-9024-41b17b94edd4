<template>
  <div :class="$style.InspectTaskInfoCardTask">
    <div
      v-for="(item, index) in [
        { label: '总计划（个）', value: data.allNum || 0, planStatus: null },
        { label: '进行中（个）', value: data.useNum || 0, planStatus: 1 },
        { label: '待开始（个）', value: data.waitOpenNum || 0, planStatus: 0 },
        { label: '已完成（个）', value: data.overdueNum || 0, planStatus: 2 },
        { label: '已停用（个）', value: data.stopNum || 0, planStatus: 3 },
      ]"
      :key="item.label"
      :class="[$style.card, $style[`bg-${index + 1}`], curCardIndex === index && $style.active]"
      @click="handleCardClick(item, index)"
    >
      <div>{{ item.value }}</div>
      <div>{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const emits = defineEmits(['change']);

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

// 当前卡片索引
const curCardIndex = ref<number | null>(0);

function handleCardClick(item: any, index: number) {
  if (curCardIndex.value === index) {
    curCardIndex.value = null;
  } else {
    curCardIndex.value = index;
  }

  emits('change', { planStatus: curCardIndex.value === null ? null : item.planStatus });
}

defineOptions({ name: 'InspectTaskInfoCardTask' });
</script>

<style module lang="scss">
.InspectTaskInfoCardTask {
  display: grid;
  grid-template-columns: repeat(5, 230px);
  gap: 10px 20px;
  justify-content: center;

  .card {
    width: 230px;
    height: 74px;
    display: flex;
    flex-direction: column;
    padding: 5px 20px;
    color: #fff;
    background-size: cover;
    background-position: center;
    border: 2px solid transparent;
    border-radius: 2px;
    cursor: pointer;
    &.bg-1 {
      background-image: url('./assets/bg1.png');
      &.active {
        border: 2px solid #45caff;
      }
    }
    &.bg-2 {
      background-image: url('./assets/bg2.png');
      &.active {
        border: 2px solid #299fff;
      }
    }
    &.bg-3 {
      background-image: url('./assets/bg3.png');
      &.active {
        border: 2px solid #b900b3;
      }
    }
    &.bg-4 {
      background-image: url('./assets/bg4.png');
      &.active {
        border: 2px solid #00db42ff;
      }
    }
    &.bg-5 {
      background-image: url('./assets/bg5.png');
      &.active {
        border: 2px solid red;
      }
    }

    > div:nth-child(1) {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }

    > div:nth-child(2) {
      font-size: 14px;
      font-weight: bold;
      opacity: 0.9;
    }
  }
}
</style>
