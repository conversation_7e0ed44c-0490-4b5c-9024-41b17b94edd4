<template>
  <n-drawer class="com-detail-drawer" v-model:show="showModal">
    <n-card
      style="width: 778px; height: 100vh; position: fixed; top: 0px; right: 0px"
      :bordered="false"
      size="huge"
      role="dialog"
      aria-modal="true"
      :class="$style['n-card']"
    >
      <div :class="$style['header']">
        <div :class="$style['left']">
          <img style="width: 18px; height: auto; margin-right: 8px" :src="detailTop" preview-disabled />
          <span style="font-weight: 600; font-size: 16px; color: #18191a">
            <!-- {{props.deviceDetail.hiveId ? '机库' : '无人机'}} -->
            设备详情
          </span>
          <!-- <p>机场</p> -->
        </div>
        <div :class="$style['right']">
          <!-- <n-image width="35" height="35" :src="close" preview-disabled /> -->
          <div :class="$style['btn-close']" @click="handleClose">
            <IconClose class="icon" />
          </div>
        </div>
      </div>

      <div :class="$style['detailListWrap']">
        <div :class="$style['detailHeader']">
          <div :class="$style['left']">
            <p>{{ props.deviceDetail.hiveId ? '机库' : '无人机' }}</p>
            <!-- <div :class="$style['status']">
              <div :class="$style['statusItem']">设备空闲中</div>
              <div :class="$style['lines']"></div>
              <div :class="$style['statusItem']">当前正常</div>
            </div> -->
          </div>
        </div>
        <div :class="$style['detailList']">
          <div :class="$style['left']">
            <img
              :class="$style['robot__image']"
              v-if="props.deviceDetail.hiveId"
              :src="imgBaseUrl + props.deviceDetail.hivePicture"
            />
            <img :class="$style['robot__image']" v-else :src="imgBaseUrl + props.deviceDetail.uavPicture" />
            <div :class="$style['status']">
              <!-- <span :class="$style['statusItem']">{{
                props.deviceDetail.hiveId ? props.deviceDetail.hiveModel : props.deviceDetail.model
              }}</span> -->
              <span :class="$style['statusItem']"
                >{{ props.deviceDetail.hiveId ? '机库' : '无人机' }}名称：{{
                  props.deviceDetail.hiveId ? props.deviceDetail.hiveName : props.deviceDetail.uavName
                }}</span
              >
              <!-- <span :class="$style['statusItem']" v-if="props.deviceDetail.uavId"
                >飞控SN：{{ props.deviceDetail.fcsn }}</span
              > -->
            </div>
            <!-- <div :class="$style['bottom']">
              <p>保养服务</p>
              <div>--</div>
            </div>
            <div :class="$style['bottom']">
              <p>行业无忧</p>
              <div>未绑定</div>
            </div> -->
          </div>
          <div :class="$style['right__box']">
            <div :class="$style['right']">
              <div :class="$style['item']">
                <p v-if="props.deviceDetail.hiveId">{{ props.deviceDetail.hiveId || '--' }}</p>
                <p v-else>{{ props.deviceDetail.uavId || '--' }}</p>
                <div>{{ props.deviceDetail.hiveId ? '机库' : '无人机' }}编号</div>
              </div>
              <div :class="$style['item']">
                <p>{{ props.deviceDetail.siteId || '--' }}</p>
                <div>站点编号</div>
              </div>
              <div :class="$style['item']" v-if="props.deviceDetail.uavId">
                <p>{{ props.deviceDetail.sn || '--' }}</p>
                <div>无人机机身序列号</div>
              </div>
              <div :class="$style['item']" v-if="props.deviceDetail.uavId">
                <p>{{ props.deviceDetail.brand || '--' }}</p>
                <div>无人机厂商</div>
              </div>
            </div>
            <div :class="$style['right']">
              <div :class="$style['item']">
                <p v-if="props.deviceDetail.hiveId">{{ props.deviceDetail.hiveModel || '--' }}</p>
                <p v-else>{{ props.deviceDetail.model || '--' }}</p>
                <div>{{ props.deviceDetail.hiveId ? '机库' : '无人机' }}型号</div>
              </div>
              <div :class="$style['item']" v-if="props.deviceDetail.uavId">
                <p>{{ props.deviceDetail.fcsn || '--' }}</p>
                <div>无人机飞控序列</div>
              </div>
              <div :class="$style['item']">
                <p v-if="props.deviceDetail.hiveId">{{ props.deviceDetail.hiveFlvUrl || '--' }}</p>
                <p v-else>{{ props.deviceDetail.uavFlvUrl || '--' }}</p>
                <div>{{ props.deviceDetail.hiveId ? '机库' : '无人机' }}FLV实时视角地址</div>
              </div>
              <!-- <div :class="$style['item']">
                <p>{{props.deviceDetail.sn || '--'}}</p>
                <div>无人机机身序列号</div>
              </div>
              <div :class="$style['item']">
                <p>{{props.deviceDetail.brand || '--'}}</p>
                <div>无人机厂商</div>
              </div> -->
            </div>
            <!-- <div :class="$style['right']">
              <div :class="$style['item']">
                <p>--</p>
                <div>累计飞行时常</div>
              </div>
              <div :class="$style['item']">
                <p>--</p>
                <div>飞行架次</div>
              </div>
              <div :class="$style['item']">
                <p>--</p>
                <div>图传</div>
              </div>
              <div :class="$style['item']">
                <p>--</p>
                <div>搜星状态</div>
              </div>
            </div>
            <div :class="$style['right']">
              <div :class="$style['item_2']">
                <h2>电池</h2>
              </div>
              <div :class="$style['item_2']">
                <p>--</p>
                <div>循环次数</div>
              </div>
              <div :class="$style['item_2']">
                <p>--</p>
                <div>高电量存储</div>
              </div>
              <div :class="$style['item_2']">
                <p>--</p>
                <div>电压</div>
              </div>
              <div :class="$style['item_2']">
                <p>--</p>
                <div>温度</div>
              </div>
              <div :class="$style['item_2']">
                <p>--</p>
                <div>电量</div>
              </div>
            </div>

            <div :class="$style['right']">
              <div :class="$style['item_3']">
                <span>飞行夜航灯</span>
                <h3>--</h3>
              </div>
              <div :class="$style['item_3']">
                <span>备降转移高</span>
                <h3>--</h3>
              </div>
              <div :class="$style['item_3']">
                <span>限高</span>
                <h3>--</h3>
              </div>
              <div :class="$style['item_3']">
                <span>限远</span>
                <h3>--</h3>
              </div>
              <div :class="$style['item_3']">
                <span>避降</span>
                <h3>--</h3>
              </div>
              <div :class="$style['item_3']">
                <span>运行模式</span>
                <h3>--</h3>
              </div>
            </div> -->
          </div>
        </div>
      </div>
      <!-- <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <n-button
            type="tertiary"
            color="#dcdfe6"
            text
            @click="handleClose"
            style="margin-right: 15px; width: 88px; color: #606266; border: 1px solid #dcdfe6; border-radius: 2px"
            >取消</n-button
          >
          <n-button type="primary" style="width: 88px">编辑</n-button>
        </div>
      </template> -->
    </n-card>
  </n-drawer>
</template>

<script lang="ts" setup>
import { PropType, ref, watch, defineProps } from 'vue';
import { CdChevronRight } from '@kalimahapps/vue-icons';
import type { DeviceDetailType2, DeviceRoomDetailType2 } from './type';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import detailTop from './assets/detailTop.png';
import close from './assets/close.png';
import { CdChromeClose as IconClose } from '@kalimahapps/vue-icons';

const imgBaseUrl = window.$SYS_CFG.fileService;

const [loading, run] = useAutoLoading(false);
let deviceID = ref('');
let showModal = ref(false);
const props = defineProps({
  deviceDetail: {
    type: Object,
    default: () => {},
  },
});
function init() {
  showModal.value = true;
}
function handleClose() {
  showModal.value = false;
}
defineExpose({
  init,
});
defineOptions({ name: 'ComCardCRef' });
</script>

<style module lang="scss">
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
  padding: 15px 25px;
  .left {
    display: flex;
    align-items: center;
    .robot__image {
      width: 118px !important;
    }
    p {
      margin-left: 8px;
      font-size: 18px;
    }
  }
}
.com-detail-drawer-footer {
  display: flex;
  justify-content: flex-end;
  .n-button {
    width: 88px;
    padding: 0px;
    &:first-child {
      color: #606266;
      border: 1px solid #dcdfe6;
      margin-right: 12px;
    }
  }
}
.btn-close {
  position: absolute;
  right: 16px;
  top: 20px;
  font-size: 20px;
  cursor: pointer;
}
.detailListWrap {
  height: calc(100vh - 70px);
  overflow-y: scroll;
  .detailHeader {
    display: flex;
    justify-content: space-between;
    margin: 24px 24px 0 24px;
    .left {
      display: flex;
      align-items: center;
      p {
        font-weight: 700;
        font-size: 16px;
        color: #222222;
      }
      .status {
        display: flex;
        margin-left: 16px;
        .statusItem {
          background: #00b578;
          color: white;
          padding: 4px;
          border-radius: 5px;
        }
        .lines {
          height: 30px;
          width: 2px;
          background: #ebeef5;
          margin: 0 12px;
        }
      }
    }
    .right {
      display: flex;
      align-items: center;
      p {
        color: #527cff;
      }
    }
  }
  .detailList {
    display: flex;
    justify-content: space-between;
    padding: 15px 24px 24px 24px;
    .left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 250px;
      .status {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-bottom: 13px;
        border-bottom: 1px solid #e6e9f0;
        .statusItem {
          font-size: 14px;
          color: #666666;
          line-height: 22px;
          margin-bottom: 4px;
        }
      }
      .bottom {
        width: 250px;
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #e6e9f0;
        p {
          font-weight: bold;
          font-size: 14px;
          color: #222222;
          line-height: 22px;
        }
        div {
          font-size: 14px;
          color: #666666;
          line-height: 22px;
        }
      }
    }
    .right__box {
      width: 456px;
      border-radius: 4px 4px 4px 4px;
    }
    .right {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 20px;
      .item {
        flex: 1;
        height: 75px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0 5px;
        p {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          font-size: 14px;
          color: #222222;
          line-height: 22px;
          height: 50px;
        }
        div {
          white-space: nowrap;
          margin-top: 4px;
          font-size: 14px;
          color: #666666;
          line-height: 22px;
        }
      }
      .item_2 {
        display: flex;
        flex: 1;
        height: 75px;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
      .item_3 {
        background-color: #f8f9fb;
        height: 35px;
        width: 48%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 1% 8px;
        padding: 0 10px;
        span {
          font-size: 14px;
          color: #aaa;
        }
        h3 {
          font-size: 14px;
          color: #333;
        }
      }
    }
  }
}
.n-card :global(.n-card__content) {
  padding: 0 !important;
}
</style>
