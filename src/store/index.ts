import pkg from '../../package.json';
import type { IAuthState, ILoginRes } from './type';
import { defineStore } from 'pinia';
import MockMenuData from './mockMenuData.json';

export const useStore = defineStore(`${pkg.name}-store`, {
  persist: true,
  state: (): IAuthState => ({
    userInfo: {
      zhName: '',
      zhLogo: '',
      zhId: '',
      id: '',
      resourceVoList: [],
      token: '',
    },
    treeAct: null, //当前选中的组织
  }),
  getters: {
    // 登录状态
    isLogin(state) {
      return Boolean(state.userInfo.token);
    },
  },
  actions: {
    reset() {
      this.$reset();
    },
    setUser(data: ILoginRes) {
      // 开发阶段使用 menu 模拟数据
      // if (import.meta.env.DEV) {
      //   data.resourceVoList[0].childrens = data.resourceVoList[0].childrens.concat(MockMenuData);
      // }

      this.userInfo = data;
    },
    setTreeAct(data: any) {
      console.log('data', data);
      this.treeAct = data;
    },
  },
});
