export function findByOrgType(data: any[], targetType: string): any | null {
  let firstFoundNode: any | null = null;

  // 辅助函数：递归处理所有节点
  function processNodes(nodes: any[]) {
    for (const item of nodes) {
      console.log('检查节点:', item);

      // 为orgType不等于'1'的节点添加disabled属性
      if (item.attributes && item.attributes.orgType !== '1') {
        item.disabled = true;
      } else {
        item.disabled = false;
      }

      // 记录第一个匹配的节点
      if (!firstFoundNode && item.attributes && item.attributes.orgType === targetType) {
        console.log('找到第一个匹配节点:', item);
        firstFoundNode = item;
      }

      // 递归处理子节点
      if (item.children && Array.isArray(item.children)) {
        processNodes(item.children);
      }
    }
  }

  // 先完整处理所有节点
  processNodes(data);

  // 最后返回第一个找到的节点
  return firstFoundNode;
}
