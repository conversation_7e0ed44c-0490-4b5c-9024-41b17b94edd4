import type { OptionalParams as fileOptionalParams } from 'js-file-downloader';
import JsFileDownloader from 'js-file-downloader';
import { useStore } from '@/store';

const isURLEncoded = (str: string) => /%[0-9A-Fa-f]{2}/g.test(str);

/**
 * 文件下载
 * @param url
 * @param options
 */
export function fileDownloader(url: string, options: fileOptionalParams = {}) {
  const store = useStore();
  const defaultOpt: fileOptionalParams = {
    timeout: 2 * 60 * 1000,
    contentTypeDetermination: 'header',
    headers: [{ name: 'Token', value: `${store.userInfo.token}` }],
  };

  return new JsFileDownloader({
    url,
    ...defaultOpt,
    ...options,
    nameCallback: (name: string) => {
      // 处理中文乱码问题
      if (isURLEncoded(name)) name = decodeURIComponent(name);
      return options.filename || name;
    },
  });
}

export const downFileUrl = (item: { url: string; fileName: string }) => {
  const a = document.createElement('a');
  a.href = item.url;
  a.download = item.fileName;
  a.click();
};
