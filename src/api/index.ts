import { stringify } from 'querystringify';
import { merge } from 'lodash-es';
// api file
import com from './com';
import video from './video';
import robot from './robot';
import uav from './uav';
import artificial from './artificial';
import hy from './hy';
import zyx from './zyx';

import { useStore } from '@/store';
export const api = {
  //type 里面的值不再拼接在请求接口上，基本接口信息从环境变量中拿
  type: {
    nil: '',
    file: 'file-server', // 文件上传服务
    intelligent: 'intelligent', //智能巡检
    artificial: 'artificial', //人工智能巡检
    login: 'comlogin', ////统一登录接口验证配置代理
    uavPost: 'uav',
    internetMonitor: 'ehs-clnt-internetMonitor-service', // 物联网监测
  },
  name: merge(com, hy, zyx),
  video: merge(video),
  robot: merge(robot),
  uav: merge(uav),
  artificial: merge(artificial),
  /**
   * 组装请求地址
   * @param serviceType
   * @param apiName
   * @param query
   * @param aux 请求拼在地址后的 如‘xxxx/aux’形式
   */
  getUrl(serviceType: string, apiName: string, query?: any, aux?: string | number): string {
    const store = useStore();
    let url = window.$SYS_CFG.intelligentInspectionService;
    //添加公共传值
    const comParam = stringify({
      zhId: store.userInfo.zhId,
      createdBy: store.userInfo.id,
      createdByName: store.userInfo.userName,
    });
    let comParamStr = query ? `&${comParam}` : `?${comParam}`;
    //匹配统一登录接口匹配
    if (serviceType === api.type.login) {
      url = window.$SYS_CFG.upmsService;
      comParamStr = '';
    }
    const paramsStr = query ? `?${stringify(query)}` : '';
    const _apiName = apiName.indexOf('/') === 0 ? apiName : '/' + apiName;
    const _aux = aux ? `/${aux}` : '';
    return `${url}${_apiName}${_aux}${paramsStr}${comParamStr}`;
  },

  //统一添加参数字段
  getComParams(serviceType: string, apiName: string, data: any, aux?: string | number) {
    const store = useStore();
    const url = window.$SYS_CFG.intelligentInspectionService;
    //匹配人工智能巡检接口
    // if (serviceType === api.type.artificial) {
    //   url = import.meta.env.VITE_APP_ARTIFICIAL;
    // }
    const _apiName = apiName.indexOf('/') === 0 ? apiName : '/' + apiName;
    let curData = {
      zhId: store.userInfo.zhId || '',
      createdBy: store.userInfo.id || '',
      createdByName: store.userInfo.userName || '',
    };
    let _aux = '';
    if (typeof data === 'object' && data !== null) {
      curData = {
        ...data,
        ...curData,
      };
    } else {
      _aux = `/${data}`;
    }
    return { url: `${url}${_apiName}${_aux}`, data: curData };
  },
};
