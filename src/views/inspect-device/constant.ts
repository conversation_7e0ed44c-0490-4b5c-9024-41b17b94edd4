import type { ICardAItem } from '@/components/card/type';
import { IObj } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}
export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  NONE = 'NONE',
  SEARCH = 'SEARCH',
  EDIT = 'EDIT',
  VIDEO = 'VIDEO',
  TREECHANGE = 'TREECHANGE',
  EXPORT = 'EXPORT',
  CARDCHANGE = 'CARDCHANGE',
  REFRESH = 'REFRESH',
  DETAIL = 'DETAIL',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.NONE]: '',
  [ACTION.DETAIL]: '详情',
  [ACTION.EXPORT]: '导出',
  [ACTION.SEARCH]: '搜索',
  [ACTION.EDIT]: '查看详情',
  [ACTION.VIDEO]: '查看视频',
  [ACTION.TREECHANGE]: '树结构选择改变',
  [ACTION.CARDCHANGE]: '卡片筛选',
  [ACTION.REFRESH]: '刷新页面',
};

export const CARDLIST: ICardAItem[] = [
  {
    label: '总数量（个）',
    value: 0,
    id: -1,
    onlineState: '',
  },
  {
    label: '在线（个）',
    value: 0,
    id: 0,
    onlineState: '0',
  },
  {
    label: '离线（个）',
    value: 0,
    id: 1,
    onlineState: '1',
  },
];

// 设备在离线状态（0：在线，1：离线，2：未接入）
export const PTIONSSTATE: { label: string; value: string }[] = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '在线',
    value: '0',
  },
  {
    label: '离线',
    value: '1',
  },
  {
    label: '未接入',
    value: '2',
  },
];

// // 定义颜色类数组
// const colorClasses = ['i1', 'i2', 'i3', 'i4', 'i5', 'i6', 'i7', 'i8'];
// // 获取颜色类的方法，支持循环
// export function getColorClass(index: number): string {
//   const colorIndex = index % colorClasses.length;
//   return colorClasses[colorIndex];
// }

// 根据字符串生成渐变色
export function generateGradientColor(str: string): string {
  // 使用字符串哈希值生成颜色
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }

  // 生成基础颜色
  const hue = Math.abs(hash) % 360;
  const saturation = 70 + (Math.abs(hash) % 30); // 70-100%
  const lightness = 40 + (Math.abs(hash) % 20); // 40-60%

  // 转换为 HSL 颜色
  const baseColor = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  const darkerColor = `hsl(${hue}, ${saturation}%, ${lightness - 30}%)`;

  // 返回渐变色
  return `linear-gradient(180deg, ${baseColor}, ${darkerColor})`;
}

// 检查原图路径是否以 ".jpg", ".png", ".jpeg" 等常见图片格式结尾
const imageExtension = /\.(jpeg|jpg|png|gif|bmp|tiff|webp)$/i;
export function replaceWithThumbnailPath(originalImagePath: string, thumbnailSuffix: string) {
  const originalExtension = originalImagePath.match(imageExtension);
  if (!originalExtension) {
    throw new Error('原图路径没有有效的图片扩展名');
  }
  if (
    originalImagePath.includes('/img1') ||
    originalImagePath.startsWith('/img1') ||
    originalImagePath.startsWith('img1')
  ) {
    originalImagePath = window.$SYS_CFG.baseURL + originalImagePath;
  } else {
    originalImagePath = window.$SYS_CFG.fileService + originalImagePath;
  }

  // 如果缩略图后缀包含文件扩展名（例如 "_80X160.jpg"），则直接使用它替换原图的扩展名
  // 否则，将缩略图后缀添加到原图的扩展名之前
  if (thumbnailSuffix.includes('.')) {
    // 缩略图后缀包含点，表示它有自己的扩展名，直接替换原图的扩展名
    return originalImagePath.replace(imageExtension, thumbnailSuffix);
  } else {
    // 缩略图后缀不包含点，表示它是一个参数或简单的后缀，添加到原图的扩展名之前
    return originalImagePath.replace(imageExtension, `${thumbnailSuffix}${originalExtension[0]}`);
  }
}
