import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';

// 获取楼栋 --传单位id
export function getBuildingListByorgCodeAPI(query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, '/video/device/getBuildingListByOrgCode', query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
// 获取楼栋--传电子档案id
export function getBuildingListByUnitIdAPI(query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, '/video/device/getBuildingListByUnitId', query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
// 获取楼层
export function getFloorListByUnitIdAndBuildingAPI(query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, '/video/device/getFloorListByUnitIdAndBuilding', query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

// 获取列表数据
export function getDevicePageAPI(query?: IObj<any>) {
  const param = api.getComParams(api.type.intelligent, '/video/device/page', query);
  return $http.post(param.url, { data: { _cfg: { showTip: true }, ...param.data } });
}
// /video/device/statisticByOnlineState
// 获取统计
export function getstatisticByOnlineStateAPI(query?: IObj<any>) {
  const param = api.getComParams(api.type.intelligent, '/video/device/statisticByOnlineState', query);
  return $http.post(param.url, { data: { _cfg: { showTip: true }, ...param.data } });
}

// 详情
export function getVideoDeviceInfoAPI(query?: IObj<any>) {
  const url = api.getUrl(api.type.intelligent, '/video/device/getVideoDeviceInfo', query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}

// 详情-------获取列表数据
export function queryVideoPlanByVideoDeviceIdAPI(query?: IObj<any>) {
  const param = api.getComParams(api.type.intelligent, '/video/device/queryVideoPlanByVideoDeviceId', query);
  return $http.post(param.url, { data: { _cfg: { showTip: true }, ...param.data } });
}

//详情---历史事件   /video/device/queryVideoTaskDisposeByVideoDeviceId
export function queryVideoTaskDisposeByVideoDeviceIdAPI(query?: IObj<any>) {
  // /video/device/queryEventListByVideoDeviceId
  const param = api.getComParams(api.type.intelligent, '/video/device/queryEventListByVideoDeviceId', query);
  return $http.post(param.url, { data: { _cfg: { showTip: true }, ...param.data } });
}
