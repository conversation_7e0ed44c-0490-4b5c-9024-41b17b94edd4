<template>
  <div :class="$style['wrap']">
    <n-form :show-feedback="false" label-placement="left">
      <n-grid class="flex-1" :cols="18" :x-gap="20" :y-gap="20">
        <!-- <n-form-item-gi :span="4" label="单位">
          <n-select
            v-model:value="filterForm.unitId"
            :options="optionsUnit"
            label-field="unitName"
            value-field="unitId"
            clearable
            placeholder="请选择单位"
            @update:value="GetoptionsbuildByunitId"
          />
        </n-form-item-gi> -->
        <n-form-item-gi :span="4" label="楼栋">
          <n-select
            v-model:value="filterForm.buildingId"
            :options="optionsbuild"
            label-field="buildingName"
            value-field="buildingId"
            clearable
            placeholder="全部"
            @update:value="GetoptionsfloorBybuild"
          />
        </n-form-item-gi>

        <n-form-item-gi :span="4" label="楼层">
          <n-select
            v-model:value="filterForm.floorId"
            :options="optionsfloor"
            label-field="floorName"
            value-field="floorId"
            clearable
            placeholder="全部"
          />
        </n-form-item-gi>

        <!-- <n-form-item-gi label="在线状态" :span="4">
          <n-select :options="PTIONSSTATE" clearable v-model:value="filterForm.onlineState" placeholder="全部" />
        </n-form-item-gi> -->

        <n-form-item-gi label="设备位置" :span="4">
          <n-input placeholder="请输入设备位置" clearable v-model:value="filterForm.deviceAddress" />
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { trimObjNull } from '@/utils/obj';
import { onMounted, ref, watch } from 'vue';
import { ACTION, PTIONSSTATE } from '../constant';
import {
  getBuildingListByorgCodeAPI,
  getBuildingListByUnitIdAPI,
  getFloorListByUnitIdAndBuildingAPI,
} from '../fetchData';
import { useStore } from '@/store';
import { storeToRefs } from 'pinia';
import { deviceService } from '../deviceService';

const store = useStore();
const { treeAct } = storeToRefs(store);
const emits = defineEmits(['action']);

const optionsbuild = ref([]);
const optionsfloor = ref([]);
const optionsUnit = ref([]);

const filterForm = ref(initForm());
function initForm() {
  return {
    // unitId: null,
    buildingId: null, //楼栋
    floorId: null, //楼层
    onlineState: null, //在线状态
    deviceAddress: '', //设备位置
  };
}
// treeAct.value?.attributes.erecordUnitId
// 单位
// function GetoptionsunitByunitId() {
//   // 获取单位列表
//   optionsUnit.value = [];
//   console.log(treeAct.value?.id, '-=-=treeAct.value?.id');
//   filterForm.value.unitId = null;
//   filterForm.value.buildingId = null;
//   filterForm.value.floorId = null;
//   // getUnitListAPI({ unitId: treeAct.value?.attributes.erecordUnitId }).then((res: any) => {
//   //   optionsUnit.value = res.data;
//   // });
// }
const erecordUnitId = ref('');
// 获取楼栋
function GetoptionsbuildByunitId(params: any) {
  filterForm.value.buildingId = null;
  filterForm.value.floorId = null;
  optionsbuild.value = [];
  optionsfloor.value = [];
  erecordUnitId.value = params.erecordUnitId;
  getBuildingListByorgCodeAPI({ ordCode: params.orgCode }).then((res: any) => {
    optionsbuild.value = res.data;
    // console.log(optionsbuild.value, '-=-=-=-=-=楼栋列表');
  });
}
// 获取楼层
function GetoptionsfloorBybuild() {
  // console.log(filterForm.value.buildingId, '-=-=-=-GetoptionsfloorBybuild');
  if (filterForm.value.buildingId) {
    filterForm.value.floorId = null;
    optionsfloor.value = [];
    getFloorListByUnitIdAndBuildingAPI({
      unitId: erecordUnitId.value,
      buildId: filterForm.value.buildingId,
    }).then((res: any) => {
      optionsfloor.value = res.data;
      // console.log(optionsfloor.value, '-=-=-=-=-=楼层列表');
    });
  } else {
    optionsfloor.value = [];
    filterForm.value.floorId = null;
  }
}

function clearForm() {
  filterForm.value = {
    buildingId: null, //楼栋
    floorId: null, //楼层
    onlineState: null, //在线状态
    deviceAddress: '', //设备位置
  };
}

function getFilterForm() {
  return trimObjNull(filterForm.value);
}

function doHandle(action: ACTION) {
  // console.log(filterForm.value, '-=-=-=-filterForm.value');
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

watch(filterForm.value, () => {
  console.log(filterForm.value, '-=-=-=-filterForm.value');
  doHandle(ACTION.SEARCH);
});
defineExpose({
  clearForm,
  GetoptionsbuildByunitId,
  emit(data: any, source?: string) {
    // console.log(data, 'propsDataaaaa');
    // 来自卡片
    if (source === 'InfoCardTask') {
      filterForm.value.onlineState = data.onlineState;
      return;
    }
  },
});
defineOptions({ name: 'FilterComp' });
</script>

<style module lang="scss">
.wrap {
  width: 100%;
  padding: 20px 0;
}
</style>
