<template>
  <div :class="$style.InspectDeviceCardTask">
    <div
      v-for="(item, index) of cardList"
      :key="item.label"
      :class="[$style.card, $style[`bg-${index + 1}`], activeIndex === item.onlineState && $style.active]"
      @click="actionFn(item, index)"
    >
      <div>{{ item.value }}</div>
      <div>{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
// import totalimg from '@/views/inspect-device/assets/total.png';
// import onlineimg from '@/views/inspect-device/assets/online.png';
// import offlineimg from '@/views/inspect-device/assets/offline.png';
import { ACTION, CARDLIST } from '@/views/inspect-device/constant';
import { IObj } from '@/types';
import { useStore } from '@/store';
import { storeToRefs } from 'pinia';
import { ICardAItem } from '@/components/card/type';
import { getstatisticByOnlineStateAPI } from '../fetchData';
import { deviceService } from '../deviceService';
const emits = defineEmits(['action']);
const store = useStore();
const { treeAct } = storeToRefs(store);

// const itmes = ref([totalimg, onlineimg, offlineimg]);

const cardList = ref<ICardAItem[]>(CARDLIST);
const activeIndex = ref<any>('');
let filterData: IObj<any> = {}; // 搜索条件
const paramsData = ref({});
//统计
function equiListRecord() {
  paramsData.value = {
    // ...filterData,
    // deptId: filterData.unitId || treeAct.value?.id,
    // deptId: treeAct.value?.id,
    deptId: filterData.deptId,
    levelCode: filterData.levelCode,
  };
  console.log(paramsData.value, '-=-=-=-=-=-=-=-=-=-=-=paramsData.value---statistic');
  getstatisticByOnlineStateAPI(paramsData.value).then((res: any) => {
    cardList.value[0].value = res.data.allNum || 0;
    cardList.value[1].value = res.data.onlineNum || 0;
    cardList.value[2].value = res.data.offlineNum || 0;
  });
}
function actionFn(item: any, index: number) {
  console.log(item, 'actionFn');
  activeIndex.value = item.onlineState;
  emits('action', {
    action: ACTION.CARDCHANGE,
    data: { onlineState: item.onlineState },
  });
}
function equiListRecordWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  equiListRecord();
}
defineExpose({ equiListRecordWrap, equiListRecord });
defineOptions({ name: 'statisticComp' });
</script>
<style module lang="scss">
.InspectDeviceCardTask {
  display: grid;
  grid-template-columns: repeat(3, 290px);
  gap: 10px 20px;
  justify-content: center;

  .card {
    width: 290px;
    height: 74px;
    display: flex;
    flex-direction: column;
    padding: 5px 20px;
    color: #fff;
    background-size: cover;
    background-position: center;
    &.bg-1 {
      background-image: url('../assets/total.png');
      &.active {
        box-shadow: inset 0 0 0 2px #45caffff;
        border-radius: 2px 2px 2px 2px;
        // filter: glow-shadow(#45caffff);
      }
    }
    &.bg-2 {
      background-image: url('../assets/online.png');
      &.active {
        box-shadow: inset 0 0 0 2px #00db42ff;
        border-radius: 2px 2px 2px 2px;
        // filter: glow-shadow(#00db42ff);
      }
    }
    &.bg-3 {
      background-image: url('../assets/offline.png');
      &.active {
        box-shadow: inset 0 0 0 2px #bcbcbcff;
        border-radius: 2px 2px 2px 2px;
        // filter: glow-shadow(#bcbcbcff);
      }
    }
    // 选中状态的边框样式 - 三种不同颜色
    // &.active-1 {
    //   box-shadow: inset 0 0 0 4px #45caffff; // （总计）
    //   // border: 2px solid #45caff;
    //   border-radius: 2px;
    // }

    // &.active-2 {
    //   box-shadow: inset 0 0 0 4px #00db42ff; // （在线）
    //   border-radius: 2px;
    // }

    // &.active-3 {
    //   box-shadow: inset 0 0 0 4px #bcbcbcff; //（离线）
    //   border-radius: 2px;
    // }
    > div:nth-child(1) {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }

    > div:nth-child(2) {
      font-size: 14px;
      font-weight: bold;
      opacity: 0.9;
    }
  }
}
</style>

<!-- <style scoped lang="scss">
.container {
  display: grid;
  grid-template-columns: repeat(auto-fill, 144px);
  gap: 10px 20px;
  // display: grid;
  // grid-gap: 20px;
  // // grid-template-columns: repeat(auto-fill, 264px);
  // // 固定三个属性
  // grid-template-columns: repeat(3, 264px);
  .item {
    width: 144px;
    height: 74px;
    display: flex;
    flex-direction: column;
    padding: 5px 20px;
    color: #fff;
    background-size: cover;
    background-position: center;
  }
}
</style> -->
