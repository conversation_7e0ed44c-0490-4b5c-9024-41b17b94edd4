import dayjs from 'dayjs';
import { h } from 'vue';
import { deviceService } from '../../deviceService.ts';
export const columnsDevice = [
  {
    title: '设备类型',
    key: 'deviceTypeName',
    ellipsis: { tooltip: true },
  },
  {
    title: '设备位置',
    key: 'buildingName',
    ellipsis: { tooltip: true },
    render(row: any) {
      return `${row.buildingName || '--'}${row.floorName || '--'}${row.deviceAddress || '--'}`;
    },
  },
  {
    title: '设备编号',
    key: 'deviceId',
    ellipsis: { tooltip: true },
  },
];
//  1：小时，2：天，3：周，4：月，5：季度，6：年
const typeObj: any = { '1': '时', '2': '日', '3': '周', '4': '月', '5': '季度', '6': '年' };
// 状态 0：待开始，1：进行中，2：已完成，3: 已停用
const stateObj: any = [
  { lable: '待开始', color: '#ddbf22' },
  { lable: '进行中', color: '#527CFF' },
  { lable: '已完成', color: '#67C23A' },
  { lable: '已停用', color: '#F56C6C' },
];

export const columnsPlan = [
  {
    title: '计划名称',
    key: 'planName',
    ellipsis: { tooltip: true },
  },
  {
    title: '计划周期',
    key: 'planStartDate',
    ellipsis: { tooltip: true },
    render(row: any) {
      return `${row.planStartDate ? dayjs(row.planStartDate).format('YYYY-MM-DD') : '--'} ~ ${row.planEndDate ? dayjs(row.planEndDate).format('YYYY-MM-DD') : '--'}`;
    },
  },
  {
    title: '计划状态',
    key: 'planStatus',
    ellipsis: { tooltip: true },
    render: (row: any) =>
      h(
        'div',
        {
          style: `color: ${stateObj[+row.planStatus].color}`,
        },
        { default: () => stateObj[+row.planStatus].lable }
      ),
  },
];
