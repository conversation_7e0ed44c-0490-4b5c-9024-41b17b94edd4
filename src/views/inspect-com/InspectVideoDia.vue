<template>
  <ComDialogC title="实时视频" :width="848" :height="410">
    <n-spin :show="loading">
      <div :class="$style.InspectVideoDia" class="com-g-col-a1 gap-x-[15px]">
        <n-scrollbar :class="$style.l" content-style="padding-bottom: 10px">
          <div class="font-bold text-[16px] mb-[5px]">设备基本信息</div>
          <div>
            <p class="flex">
              <span class="shrink-0">楼层楼栋：</span>
              <n-ellipsis line-clamp="1">
                {{
                  (detail.erecordDeviceInfo?.buildingName || '') + (detail.erecordDeviceInfo?.floorName || '') || '--'
                }}
              </n-ellipsis>
            </p>
            <p class="flex">
              <span class="shrink-0">设备位置：</span>
              <n-ellipsis line-clamp="1">{{ detail.erecordDeviceInfo?.deviceAddress || '--' }}</n-ellipsis>
            </p>
            <p class="flex">
              <span class="shrink-0">品牌型号：</span>
              <n-ellipsis line-clamp="1">
                {{ (detail.erecordDeviceInfo?.brand || '') + (detail.erecordDeviceInfo?.model || '') || '--' }}
              </n-ellipsis>
            </p>
            <p class="flex">
              <span class="shrink-0">设备编号：</span>
              <n-ellipsis line-clamp="1">{{ detail.erecordDeviceInfo?.deviceId || '--' }}</n-ellipsis>
            </p>
          </div>

          <div class="font-bold text-[16px] mb-[5px] mt-[20px]">设备算法</div>
          <div :class="$style.algGroup" v-if="algoList.length">
            <div v-for="item of algoList" :key="item.algoName">
              <n-button round size="small" :style="item.dictStyle" class="!cursor-default">
                {{ item.algoName }}
              </n-button>
            </div>
          </div>
          <div v-else>--</div>
        </n-scrollbar>

        <div :class="$style.r">
          <iframe
            v-if="detail.videoPlayUrl"
            :src="getIframeSrc()"
            frameborder="0"
            allow="autoplay; fullscreen"
          ></iframe>
        </div>
      </div>
    </n-spin>
  </ComDialogC>
</template>

<script setup lang="ts">
import ComDialogC from '@/components/dialog/ComDialogC.vue';
import { computed, ref, useAttrs, watch } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { IVideoDeviceInfo, IbmAlgoDict } from './IVideoDeviceInfo.ts';
import { getVideoDeviceInfo } from './fetchData.ts';
import { getVideoPlayerUrl } from '@/components/player/util.ts';

const $attr = useAttrs();

const props = defineProps({
  deviceId: String,
});

const [loading, wrapFn] = useAutoLoading(false);
const detail = ref<Partial<IVideoDeviceInfo>>({});
const algoList = computed<IbmAlgoDict[]>(() => {
  return detail.value.ibmDeviceInfoVo?.algoList || [];
});

function getIframeSrc() {
  const url = detail.value.videoPlayUrl;

  if (url) {
    return getVideoPlayerUrl(url);
  }

  return '';
}

function getData() {
  wrapFn(
    getVideoDeviceInfo({
      // deviceId: '20250213104230916795',
      deviceId: props.deviceId,
    })
  ).then((res) => {
    detail.value = res.data;
  });
}

function reset() {
  detail.value = {};
}

watch(
  () => $attr.show,
  (val) => {
    if (val) {
      getData();
    } else {
      reset();
    }
  }
);

defineOptions({ name: 'InspectVideoDia' });
</script>

<style module lang="scss">
.InspectVideoDia {
  backdrop-filter: blur(3px);

  .l {
    width: 225px;
    height: 315px;
    line-height: 1.6;

    .algGroup {
      & > div {
        margin: 10px 0;
      }
    }
  }

  .r {
    aspect-ratio: 16/9;
    background: #000;

    iframe {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
