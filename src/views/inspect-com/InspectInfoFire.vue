<template>
  <div :class="$style.InspectInfoFire">
    <ComBoxB title="火警信息" :is-empty="false">
      <div>
        <p>首次接收时间：{{ detail.eventTime || '--' }}</p>
        <p>末次接收时间：{{ detail.lastEventTime || '--' }}</p>
      </div>
    </ComBoxB>

    <ComBoxB
      title="现场照片"
      :is-empty="false"
      v-if="
        detail.eventType == '1' &&
        detail.eventSourceType == '0' &&
        detail.fireDisposeAttchList &&
        detail.fireDisposeAttchList.length
      "
    >
      <n-image-group :render-toolbar="renderCustomToolbar">
        <div :class="$style.imageGroup">
          <n-image
            v-for="item of detail.fireDisposeAttchList"
            :key="item"
            :src="getFullThumbnailUrl(item, '100x80')"
            :preview-src="getFullFileUrl(item)"
            alt=""
          />
        </div>
      </n-image-group>
    </ComBoxB>

    <!--    <ComBoxB title="安消联动" :is-empty="false">-->
    <!--      <div>todo</div>-->
    <!--    </ComBoxB>-->

    <ComBoxB title="设备信息" :is-empty="false">
      <div>
        <p>单位名称：{{ detail.unitName || '--' }}</p>
        <p>设备编号： {{ detail.deviceNum || '无' }}</p>
        <p v-if="detail.deviceClassification == '1' && switchCode(detail) != '--'">
          主机回路点位：{{ switchCode(detail) }}
        </p>
        <p v-if="detail.deviceClassification == '1'">二次码： {{ detail.twoCode || '未采集' }}</p>
        <p>系统类型：{{ detail.deviceTypePname || '未知系统' }}</p>
        <p>设备类型：{{ detail.deviceTypeName || '未知设备' }}</p>
        <p class="flex">
          <span class="shrink-0">设备位置：</span>
          <n-ellipsis line-clamp="1">{{ detail._deviceAddr }}</n-ellipsis>
          <n-button
            class="shrink-0 !ml-[10px]"
            type="primary"
            text
            size="small"
            @click="handleLocationClick"
            :disabled="!detail.deviceId"
          >
            <template #icon>
              <n-icon><IconLocation /></n-icon>
            </template>
            位置
          </n-button>
        </p>
        <p class="flex">
          <span class="shrink-0">品牌型号：</span>
          <n-ellipsis line-clamp="1">{{ detail._produceBrandAndModel }}</n-ellipsis>
        </p>
        <p>安装日期：{{ detail._installDate }}</p>
      </div>
    </ComBoxB>

    <ComBoxB title="处置信息" :is-empty="false">
      <div>
        <p>处置状态：{{ detail.disposeStateStr || '--' }}</p>
        <p>处置时间：{{ detail.disposeTime || '--' }}</p>
        <p>处置动作：{{ detail.disposeActionStr || '--' }}</p>
        <p>处置结果：{{ detail.disposeResultStr || '--' }}</p>
      </div>
    </ComBoxB>

    <ComBoxB title="复位信息" :is-empty="false">
      <div>
        <p>复位时间：{{ detail.resetTime || '--' }}</p>
        <p>是否规范：{{ detail.resetState == 1 ? detail.isRegular || '--' : '--' }}</p>
        <p v-if="detail.isRegular != '规范'">
          不规范原因：{{ detail.resetState == 1 ? detail.regularDesc || '--' : '--' }}
        </p>
        <p v-if="detail.isRegular == '不规范'">规范时间： {{ detail.checkTime + '分钟' || '--' }}</p>
      </div>
    </ComBoxB>

    <DeviceLocation v-model:show="showDrawer" :device-list="deviceList" />
  </div>
</template>

<script setup lang="ts">
import ComBoxB from '@/components/box/ComBoxB.vue';
import { FlFilledLocationRipple as IconLocation } from '@kalimahapps/vue-icons';
import { getDisposeEventFireInfo } from './fetchData.ts';
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/fileUrl.ts';
import { IObj } from '@/types';
import { ref, computed } from 'vue';
import { renderCustomToolbar } from '@/common/hooks/useImagePreview.ts';
import { normalizeAddress, switchCode } from './util.ts';
import DeviceLocation from './DeviceLocation.vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

const detail = ref<IObj<any>>({});
// 设备信息
const deviceList = ref<any[]>([]);

const showDrawer = ref(false);
function handleLocationClick() {
  showDrawer.value = true;
}

function getData() {
  getDisposeEventFireInfo({
    disposeId: props.data.disposeId,
    eventType: props.data.disposeEventType,
  }).then((res) => {
    // 调试代码 ->
    // if (import.meta.env.DEV) {
    //   res.data.fireDisposeAttchList = [
    //     '20250815/7c9ea6ef5f6a481f876d6c7fde5bd7d5.jpg',
    //     '20250815/7c9ea6ef5f6a481f876d6c7fde5bd7d5.jpg',
    //     // todo
    //   ];
    // }
    // 调试代码 <-

    const data = res.data || {};

    try {
      data._deviceAddr = normalizeAddress(data);

      const produceInfo = res.data.produceInfo ? JSON.parse(res.data.produceInfo) : {};
      data._produceBrandAndModel = (produceInfo.brand || '') + (produceInfo.model || '') || '--';

      const installInfo = res.data.installInfo ? JSON.parse(res.data.installInfo) : {};
      data._installDate = installInfo?.installDate || '--';
    } catch (e) {}

    detail.value = data;

    deviceList.value.push({
      deviceId: data.deviceId,
      deviceNum: data.deviceNum,
      deviceTypeId: data.deviceTypeId,
      deviceAddress: data.deviceAddress,
      buildingId: data.buildingId,
      floorId: data.floorId,
      mapX: data.mapX,
      mapY: data.mapY,
      mapZ: data.mapZ,
      latitude: data.latitude,
      longitude: data.longitude,
    });
  });
}

// init
getData();

defineOptions({ name: 'InspectInfoFire' });
</script>

<style module lang="scss">
.InspectInfoFire {
  > div {
    margin-bottom: 15px;
  }

  .imageGroup {
    display: grid;
    grid-template-columns: repeat(auto-fit, 100px);
    gap: 20px;

    > div {
      background: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
