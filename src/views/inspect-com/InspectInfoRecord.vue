<template>
  <div :class="$style.InspectInfoRecord">
    <ComBoxC>
      <n-timeline-item line-type="dashed" v-for="node of dataList" :key="node.id">
        <template #header>{{ node.nodeName }}</template>

        <div
          :class="$style.nodeWrap"
          class="com-g-col-a1"
          v-for="(item, pIndex) of sortNodeInfo(node.nodeInfo || [])"
          :key="pIndex"
        >
          <span> {{ descriptionStr(item, pIndex) }}：</span>

          <!-- 图片 -->
          <n-image-group v-if="item.webType == 'image'" :render-toolbar="renderCustomToolbar">
            <div :class="$style.imageGroup">
              <n-image
                v-for="(img, imgIdx) of item.dataValue"
                :key="imgIdx"
                :src="getFullThumbnailUrl(img, '80x64')"
                :preview-src="getFullFileUrl(img)"
                alt=""
              />
            </div>
          </n-image-group>
          <!-- 音频 -->
          <div v-else-if="item.webType == 'audio'">--</div>
          <!-- 文本 -->
          <span v-else>{{ item.dataValue }}</span>
        </div>
      </n-timeline-item>
    </ComBoxC>
  </div>
</template>

<script setup lang="ts">
import ComBoxC from '@/components/box/ComBoxC.vue';
import { ref } from 'vue';
import { getDisposeEventFireRecord, getDisposeEventHazardRecord } from './fetchData.ts';
import { sortBy } from 'lodash-es';
import { renderCustomToolbar } from '@/common/hooks/useImagePreview.ts';
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/fileUrl.ts';

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

const dataList = ref<any[]>([]);

const descriptionStr = (item: any, pIndex: number) => {
  let description = item.description;
  if (dataList.value.length - 1 !== pIndex && item.description.includes('平台首次接收时间')) {
    description = item.description.replace('平台首次接收时间', '平台接收时间');
  }
  return description;
};

function sortNodeInfo(data: any[]) {
  return sortBy(data, 'sort');
}

function getData() {
  const fn = props.data.disposeEventType == 4 ? getDisposeEventHazardRecord : getDisposeEventFireRecord;

  fn({
    disposeId: props.data.disposeId,
    // disposeId: 'df4e5f4c1c7fb03b9fcd2c2062d6dc32',
    eventType: props.data.disposeEventType,
    hazardEventId: '',
  }).then((res) => {
    dataList.value = sortNodeInfo(res.data.rows || []);
  });
}

// init
getData();

defineOptions({ name: 'InspectInfoRecord' });
</script>

<style module lang="scss">
.InspectInfoRecord {
  .nodeWrap {
    line-height: 2;
  }

  .imageGroup {
    display: grid;
    grid-template-columns: repeat(auto-fit, 80px);
    gap: 15px;
    margin-top: 10px;

    > div {
      background: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
