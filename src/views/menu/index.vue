<template>
  <n-layout has-sider>
    <n-layout-sider
      :class="{ [$style.wrap]: true, [$style.expand]: isExpand }"
      :collapsed-width="48"
      :collapsed="isExpand"
      :width="240"
      @collapse="setExpand(false)"
      @expand="setExpand(true)"
      collapse-mode="width"
    >
      <!-- 自定义折叠按钮 -->
      <div :class="$style.customTrigger" @click="expand">
        <img src="./assets/icon/open-close.png" alt="开启关闭" style="width: 17px; height: 17px" />
        <div :class="$style.tit">智能巡检系统</div>
      </div>
      <n-scrollbar :style="{ 'max-height': scrollMaxH, height: 'calc(100% - 51px - 10px)' }">
        <n-menu
          ref="menuInstRef"
          v-model:value="activeKey"
          :default-expand-all="false"
          :collapsed="isExpand"
          :options="menuListArr"
          :render-icon="renderMenuIcon"
          label-field="resName"
          key-field="resAlias"
          children-field="childrens"
          :collapsed-icon-size="18"
          :inverted="isExpand"
          :accordion="false"
          :root-indent="18"
          @update:value="handleUpdateValue"
        />
      </n-scrollbar>
    </n-layout-sider>
  </n-layout>
</template>

<script lang="ts" setup>
// import { icon_expand as IconExpand } from './assets/icon/index';
import { useMenu } from '@/views/menu/useMenu';
import { computed, ref, onMounted, h, onBeforeMount } from 'vue';
import { useState } from '@/common/hooks/useState.ts';
import { useStore } from '@/store';
const store = useStore();
let menuListArr: any[] = [];
console.log(store.userInfo, 'userInfo');

const { menuList, activeKey, handleUpdateValue } = useMenu();

const [isExpand, setExpand] = useState(false);
const headerT = computed(() => {
  return store.userInfo.zhName || '';
});

const props = defineProps({
  headless: Boolean,
});
const filteredMenuList = ref([]);
// const initMenuList = () => {
//   console.log(JSON.parse(JSON.stringify(store.userInfo?.resourceVoList?.[0]?.childrens)), '--=-=-=-');
//   filteredMenuList.value = filterMenuByResType(
//     JSON.parse(JSON.stringify(store.userInfo?.resourceVoList?.[0]?.childrens))
//   );
//   console.log(filteredMenuList.value, 'filteredMenuList-=-=-=-=-=-=-=-=');
// };
// 递归过滤函数，过滤掉resType为'2'的项
const filterMenuByResType = (menuList: any) => {
  return menuList.filter((item: any) => {
    // console.log(item, 'item-=-=-=-=-=-');
    if (item.resType !== '2') {
      item.routeName = item.routeName || item.resAlias;
      item.resIcon = item.resIconUrl;
      // 如果有子菜单，递归过滤子菜单
      console.log(item.childrens && item.childrens.length > 0, 'item.childrens && item.childrens.length > 0');
      if (item.childrens && item.childrens.length > 0) {
        // 如果过滤后子菜单为空，则删除childrens属性
        if (item.childrens.length === 0) {
          delete item.childrens;
        }

        item.childrens = filterMenuByResType(item.childrens);
      } else {
        delete item.childrens;
      }
      return true;
    }
    return false;
  });
};

//递归删除icon返回数组
const delIcon = (arr: any) => {
  arr.forEach((item: any) => {
    delete item.icon;
    if (item.childrens && item.childrens.length > 0) {
      delIcon(item.childrens);
    }
  });
  return arr;
};
let menu = store.userInfo?.resourceVoList || [];
// menuListArr = menu.map((item: any) => {
//   return {
//     ...item,
//     // label: item.resName,
//     // key: item.resAlias,
//     // routeName: item.resAlias,
//     // resIcon: item.resIconUrl,
//   };
// });
menuListArr = delIcon(filterMenuByResType(menu));
console.log(delIcon(filterMenuByResType(menuListArr)), 'menuListArr');
// 批量渲染菜单svg图标
function renderMenuIcon(option: any) {
  // console.log(option, '-=-=-=-=-=-=-=-=option');
  if (option.resIconUrl) {
    return h(
      'img',
      {
        src: window.$SYS_CFG.baseURL + '/' + option.resIcon,
        width: 24,
        height: 24,
      },
      { default: () => null }
    );
  } else {
    return '';
  }
}
// menu.map((item: any) => {
//   menuList.map((attr) => {
//     if (item.resAlias == attr.routeName) {
//       menuListArr.push(attr);
//     }
//   });
// });

const scrollMaxH = computed(() => {
  return props.headless ? 'calc(100vh - 48px)' : 'calc(100vh - 64px - 48px)';
});
function expand() {
  setExpand(!isExpand.value);
}

defineOptions({ name: 'MenuIndex' });
</script>

<style module lang="scss">
.wrap {
  background: rgba(37, 40, 67, 1) !important;
  color: #fff;

  :global(.n-menu-item) {
    &:hover {
      background-color: #363d64;
    }
  }
}

.customTrigger {
  padding: 12px 22px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  overflow: hidden;

  .tit {
    margin-left: 11px;
    white-space: nowrap;
    font-size: 16px;
  }
}

.expand .customTrigger {
  padding: 11px 16px;
  transition: all 0.5s;

  .tit {
    width: 0;
    opacity: 0;
    overflow: hidden;
  }
}
</style>
