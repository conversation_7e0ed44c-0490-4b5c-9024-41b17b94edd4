<template>
  <div :class="$style.InspectTaskDetail" class="com-g-row-a1">
    <ComBread :data="breadData" />

    <div :class="$style.backBtn" class="items-center" @click="back()">返回</div>

    <div :class="$style.content">
      <div :class="$style.topBar">
        <span>
          {{ getFormatTimeRange(baseInfo.taskPlanStartTime, baseInfo.taskPlanEndTime) }} {{ baseInfo.planName }}
        </span>
        <n-button :color="TASK_STATUS_COLORS[baseInfo.taskStatus]" size="tiny">
          {{ baseInfo.taskStatusName || '--' }}
        </n-button>
      </div>

      <div>
        <ComHeaderD title="巡检基本信息" />
        <div :class="$style.baseInfo">
          <span>所属单位：{{ baseInfo.deptName || '--' }}</span>
          <span>计划名称：{{ baseInfo.planName || '--' }}</span>
          <span>任务周期：{{ getFormatTimeRange(baseInfo.taskPlanStartTime, baseInfo.taskPlanEndTime) }}</span>
          <span>视频点位：{{ baseInfo.allNum || 0 }}个</span>
        </div>
      </div>

      <ComTabF class="!inline-flex" :tab-list="tabList" :tab="curTab" @change="handleTabChange" />

      <component :is="curViewComp" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import ComBread from '@/components/breadcrumb/ComBread.vue';
import ComHeaderD from '@/components/header/ComHeaderD.vue';
import ComTabF from '@/components/tab/ComRadioTabF.vue';
import ViewList from './comp/ViewList.vue';
import ViewMatch from './comp/ViewMatch.vue';
import { computed, ref } from 'vue';
import { emptyable } from '@/types';
import { getTaskTopDetail } from '@/views/inspect-task/fetchData.ts';
import { IBreadData } from '@/components/breadcrumb/type.ts';
import { ITaskTopDetail } from './type.ts';
import { useRoute, useRouter } from 'vue-router';
import { getFormatTimeRange } from '@/utils/format.ts';
import { TASK_STATUS_COLORS } from '@/views/common/contant';
import { EventSvc } from '@/views/inspect-task/EventSvc.ts';

const route = useRoute();
const router = useRouter();

const breadData: IBreadData[] = [{ name: '视频智能巡检' }, { name: '巡检任务管理' }, { name: '任务详情' }];
const tabList = [
  { name: '1', label: '巡检详情' },
  { name: '2', label: '巡检清单' },
];

type ITab = (typeof tabList)[number]['name'];

const curTab = ref<ITab>('1');
const viewCompMap: Record<ITab, any> = {
  '1': ViewMatch,
  '2': ViewList,
};
const curViewComp = computed(() => viewCompMap[curTab.value]);
const baseInfo = ref<emptyable<ITaskTopDetail>>({});

function handleTabChange(val: ITab) {
  curTab.value = val;
}

function back() {
  router.back();
}

function getData() {
  getTaskTopDetail({
    taskId: route.query.taskId,
  }).then((res) => {
    baseInfo.value = res.data || {};
    EventSvc.taskTopDetail$.next(baseInfo.value);
  });
}

// init
getData();

defineOptions({ name: 'InspectTaskDetail' });
</script>

<style module lang="scss">
.InspectTaskDetail {
  position: relative;

  .content {
    display: grid;
    grid-template-rows: auto auto auto minmax(0, 1fr);
    grid-template-columns: minmax(0, 1fr);
    min-height: 0;

    & > div,
    & > ul {
      margin-bottom: 20px;
    }
  }

  .topBar {
    display: inline-flex;
    align-items: center;
    height: 44px;
    min-width: 200px;
    margin-bottom: 5px !important;
    padding: 0 20px;
    column-gap: 15px;
    border-radius: 6px;
    background: #092863;
    border: 1px solid var(--skin-bd2);
  }

  .baseInfo {
    height: 90px;
    border: 1px solid #0081ff;
    display: flex;
    align-items: center;
    padding: 0 30px;

    > span {
      font-size: 14px;
      margin-right: 88px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .backBtn {
    position: absolute;
    top: 10px;
    right: 24px;
    width: 78px;
    height: 36px;

    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(180deg, #0d1e3b 0%, #2251a1 100%);
    border-radius: 2px;
    border: 1px solid #3371dc;

    &:hover {
      /* 添加悬停效果 */
      background: linear-gradient(180deg, #2251a1 0%, #0d1e3b 100%); /* 反转渐变色 */
      box-shadow: 0 0 5px rgba(51, 113, 220, 0.8); /* 添加发光效果 */
    }
  }
}
</style>
