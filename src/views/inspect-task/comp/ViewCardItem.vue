<template>
  <div :class="$style.InspectTaskViewCardItem" class="com-g-row-a1">
    <n-ellipsis :class="$style.title">
      {{ data.deviceAddress || '--' }}
    </n-ellipsis>

    <div :class="$style.index">
      <span class="font-bold text-[16px]">{{ padStart(data.videoSort + '', 3, '0') }}</span>
      <span class="text-[12px]">巡检点</span>
    </div>

    <div :class="$style.trigger" title="查看详情" @click="emits('show-detail-dia', data)">
      <n-button text>
        <template #icon>
          <n-icon :size="26"><IconExpand class="rotate-90 w-[20px]" /></n-icon>
        </template>
      </n-button>
    </div>

    <div v-if="data.videoUrl" :class="$style.imageWrap">
      <div class="w-full h-[90%] relative">
        <n-image
          class="w-full h-full"
          :render-toolbar="renderCustomToolbar"
          :src="getFullThumbnailUrl(data.videoUrl, '277x160')"
          :preview-src="getFullFileUrl(data.videoUrl)"
          alt=""
        />
        <div :class="$style.time">巡检时间：{{ data.videoTime }}</div>
      </div>
    </div>
    <div v-else :class="$style.imageEmptyWrap">
      <img src="../assets/card-no-pic.png" alt="" />
      <p v-if="data.videoResult == 1">任务开始后自动拍摄巡检截图</p>
    </div>

    <div v-if="data.videoResult == 2 && data.eventTypeNames" :class="$style.eventNames">
      <n-ellipsis>
        {{ data.eventTypeNames }}
      </n-ellipsis>
    </div>
  </div>
</template>

<script setup lang="ts">
import { renderCustomToolbar } from '@/common/hooks/useImagePreview.ts';
import { padStart } from 'lodash-es';
import { MdExpandLess as IconExpand } from '@kalimahapps/vue-icons';
import { ITaskInspectionDetailPageItem } from '../type.ts';
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/fileUrl.ts';

const props = defineProps({
  data: {
    type: Object as () => ITaskInspectionDetailPageItem,
    default: () => {},
  },
});
const emits = defineEmits(['show-detail-dia']);

defineOptions({ name: 'InspectTaskViewCardItem' });
</script>

<style module lang="scss">
.InspectTaskViewCardItem {
  .title {
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0 10px 12%;
    max-width: 100%;
  }

  .index {
    position: absolute;
    left: vw(20);
    top: vw(20);
    width: 42px;
    height: 42px;
    background: #1c6fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    line-height: 1.1;
    border-radius: 4px;
  }

  .trigger {
    position: absolute;
    top: 50%;
    right: 4%;
  }

  .imageWrap {
    display: flex;
    justify-content: center;
    width: 80%;
    margin-left: 12%;

    img {
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.15);
    }

    .time {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 28px;
      line-height: 28px;
      font-size: 14px;
      padding: 0 7px;
      background: rgba(14, 45, 99, 0.75);
      backdrop-filter: blur(3px);
    }
  }

  .imageEmptyWrap {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #6b7b99;
    user-select: none;
  }

  .eventNames {
    position: absolute;
    left: 14%;
    bottom: 3%;
    color: #fa5151;
    font-size: 14px;
    width: 76%;
    overflow: hidden;
  }
}
</style>
