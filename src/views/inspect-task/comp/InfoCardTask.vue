<template>
  <div :class="$style.InspectTaskInfoCardTask">
    <div
      v-for="(item, index) of cards"
      :key="item.label"
      :class="[$style.card, $style[`bg-${index + 1}`], curCardIndex === index && $style.active]"
      @click="handleCardClick(item, index)"
    >
      <div>{{ item.value }}</div>
      <div>{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getStatistics } from '../fetchData.ts';
import { IObj } from '@/types';
import { IInspectTaskOverViewStatistics } from '../type.ts';

const emits = defineEmits(['change']);

interface CardItem {
  label: string;
  value: number;
  taskStatus: string | null;
  key: keyof IInspectTaskOverViewStatistics;
}

const unitId = ref('');
const planId = ref('');

const cards = ref<CardItem[]>([
  { label: planId.value ? '计划任务数（个）' : '总任务（个）', value: 0, key: 'taskTotal', taskStatus: null },
  { label: '进行中（个）', value: 0, key: 'taskInProgressCount', taskStatus: '2' },
  { label: '待开始（个）', value: 0, key: 'taskToBeginCount', taskStatus: '0' },
  { label: '已完成（个）', value: 0, key: 'taskCompletedCount', taskStatus: '3' },
]);

// 当前卡片索引
const curCardIndex = ref<number | null>(0);

function handleCardClick(item: CardItem, index: number) {
  if (curCardIndex.value === index) {
    curCardIndex.value = null;
  } else {
    curCardIndex.value = index;
  }

  emits('change', { taskStatus: curCardIndex.value === null ? null : item.taskStatus });
}

function getData() {
  getStatistics({
    unitId: unitId.value,
    planId: planId.value,
  }).then((res) => {
    const data = res.data || {};
    // 按key映射值
    cards.value = cards.value.map((c) => {
      const value = Number(data[c.key] ?? 0);
      return { ...c, value };
    });
  });
}

defineExpose({
  emit(data: IObj<any>, propsData: IObj<any>) {
    // 模式不同数据来源有区分
    if (propsData.pageMode === 'NARROW') {
      unitId.value = propsData.unitId;
      planId.value = propsData.planId;
    } else {
      unitId.value = data.id;
    }
    getData();
  },
});

defineOptions({ name: 'InspectTaskInfoCardTask' });
</script>

<style module lang="scss">
.InspectTaskInfoCardTask {
  display: grid;
  grid-template-columns: repeat(4, 290px);
  gap: 10px 20px;
  justify-content: center;

  .card {
    width: 290px;
    height: 74px;
    display: flex;
    flex-direction: column;
    padding: 5px 20px;
    color: #fff;
    background-size: cover;
    background-position: center;
    border: 2px solid transparent;
    border-radius: 2px;
    cursor: pointer;

    &.bg-1 {
      background-image: url('../assets/bg-c1.png');
      &.active {
        border: 2px solid #45caff;
      }
    }
    &.bg-2 {
      background-image: url('../assets/bg-c2.png');
      &.active {
        border: 2px solid #299fff;
      }
    }
    &.bg-3 {
      background-image: url('../assets/bg-c3.png');
      &.active {
        border: 2px solid #b900b3;
      }
    }
    &.bg-4 {
      background-image: url('../assets/bg-c4.png');
      &.active {
        border: 2px solid #00db42;
      }
    }

    > div:nth-child(1) {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }

    > div:nth-child(2) {
      font-size: 14px;
      font-weight: bold;
      opacity: 0.9;
    }
  }
}
</style>
