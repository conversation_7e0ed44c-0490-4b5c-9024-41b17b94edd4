<template>
  <div :class="$style.InspectTaskViewMatch">
    <template v-if="isOnlyTable">
      <ViewCard />
    </template>
    <template v-if="isMultipleUI">
      <div :class="[$style.switch, curType === 'map' ? $style.card : $style.map]" @click="toggleView">
        {{ curViewLabel }}
      </div>
      <keep-alive>
        <component :is="curViewComp"></component>
      </keep-alive>
    </template>
  </div>
</template>

<script setup lang="ts">
import ViewCard from './ViewCard.vue';
import ViewMap from './ViewMap.vue';
import { computed, ref } from 'vue';
import { getUnitInfo } from '@/gis-floor/floorGisService';
import { useRoute } from 'vue-router';

const route = useRoute();

// UI模式 - (serviceModelCode: 5)
const isOnlyTable = ref(false);
const isMultipleUI = ref(false);

const curType = ref<'card' | 'map'>('card');
const curViewLabel = computed(() => {
  if (curType.value === 'map') {
    return '卡片视图';
  }
  return '可视化视图';
});
const curViewComp = computed(() => {
  if (curType.value === 'map') {
    return ViewMap;
  }
  return ViewCard;
});

/**
 * 切换视图类型
 */
function toggleView() {
  const val = curType.value === 'map' ? 'card' : 'map';

  curType.value = val;
}

const getUnitData = async () => {
  const _id = route.query.unitId as string;
  const serviceModelCode: any = await getUnitInfo(_id);
  if (serviceModelCode === 5) {
    isOnlyTable.value = true;
  } else {
    isMultipleUI.value = true;
    curType.value = 'map';
  }
};
getUnitData();

defineOptions({ name: 'InspectTaskViewMatch' });
</script>

<style module lang="scss">
.InspectTaskViewMatch {
  position: relative;
  margin-bottom: 0 !important;

  .switch {
    position: absolute;
    right: 0;
    top: -62px;
    width: 150px;
    height: 48px;
    cursor: pointer;

    display: flex;
    align-items: center;
    padding-left: 51px;
    font-size: 14px;

    &.card {
      background: url('../assets/bg-switch-card.png') 0 0 no-repeat;
    }

    &.map {
      background: url('../assets/bg-switch-map.png') 0 0 no-repeat;
    }
  }
}
</style>
