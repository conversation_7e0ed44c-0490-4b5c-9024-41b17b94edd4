<template>
  <div class="w-full h-full com-g-row-a1 InspectAddPlanTable">
    <Filter @search="getTableDataWarp" />

    <div class="min-h-0">
      <!-- v-model:checked-row-keys="checkedRows"  :default-checked-row-keys="defaultCheckedRows"-->
      <n-data-table
        class="uncheckAllTable"
        remote
        striped
        :row-key="(rowData: any) => rowData.deviceId"
        v-model:checked-row-keys="checkedRows"
        :columns="columns"
        :data="tableData"
        :bordered="false"
        :flex-height="flexHeight"
        :pagination="pagination"
        :loading="loading"
        :render-cell="useEmptyCell"
        scroll-x=""
        @update:checked-row-keys="handleCheck"
      />
    </div>

    <VideoDia v-model:show="showVideoDia" :device-id="videoId || ''" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, inject, h, Ref } from 'vue';
import { useRoute } from 'vue-router';
import { cols } from './columns';
import { DataTableColumns, NButton } from 'naive-ui';
import Filter from './filter.vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { IObj } from '@/types';
import { IPlanBaseInfo } from '@/views/inspect-plan/type';
import { getDevicesListPage } from '@/views/inspect-plan/fetchData';
import { IDeviceRow } from '@/views/inspect-plan/type';
import { PlanService } from '@/views/inspect-plan/updatePlan/planService';
// import { PlanService } from '../../planService.ts';
import VideoDia from '@/views/inspect-com/InspectVideoDia.vue';
import { useActionWarning } from '@/common/hooks/comfirm.ts';

defineOptions({ name: 'SelectPointTable' });

const flexHeight = computed(() => {
  return tableData.value.length === 0 ? false : true;
});
const route = useRoute();

const isEdit = !!route.query.id;

const showVideoDia = ref(false);
const videoId = ref('');

let filterData: IObj<any> = ref({}); // 搜索条件

const [loading, search] = useAutoLoading(false);
const { pagination, updateTotal } = useNaivePagination(getTableData);

const tableData = ref<IDeviceRow[]>([]);
function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData.value,
  };
  search(getDevicesListPage(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWarp(filterForm: any) {
  filterData.value = filterForm;
  getTableData();
}

const columns = ref<DataTableColumns>([]);
function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 180,
    align: 'center',
    fixed: 'right',
    render(row: any) {
      return [
        h(
          NButton,
          {
            color: '#fff',
            ghost: true,
            size: 'small',
            onClick: () => {
              videoId.value = row.deviceId;
              showVideoDia.value = true;
            },
          },
          { default: () => '查看视频' }
        ),
      ];
    },
  });
}

// select rows
const checkedRows = ref<string[]>([]);
// const defaultCheckedRows = computed(() => PlanService.inspectDevices.value.map((item) => item.deviceId));

const handleCheck = (
  keys: Array<string>,
  rows: IDeviceRow[],
  meta: { row: IDeviceRow | undefined; action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll' }
) => {
  const { row, action } = meta;
  if (row && action === 'check') {
    PlanService.inspectDevices.value.push(row);
  }
  if (row && action === 'uncheck') {
    const index = PlanService.inspectDevices.value.findIndex((item) => item.deviceId === row.deviceId);
    if (index > -1) {
      PlanService.inspectDevices.value.splice(index, 1);
    }
  }
};

watch(
  () => PlanService.inspectDevices.value,
  (newValue, oldValue) => {
    checkedRows.value = newValue.map((item) => item.deviceId);
  },
  {
    deep: true,
    immediate: true,
  }
);

// watch(
//   () => PlanService.curBuildId.value,
//   (newV: string | null, oldV: string | null) => {
//     console.log(PlanService.inspectDevices.value);
//     if (!newV) return;
//     PlanService.curFloorId.value = null;
//     if (PlanService.inspectDevices.value.length > 0) {
//       useActionWarning({ msg: '当前填写的信息将不会保存', negaT: '取消' })
//         .then(() => {
//           PlanService.inspectDevices.value = [];
//           // getFloorOpt(newV);
//         })
//         .catch(() => {
//           PlanService.curBuildId.value = oldV;
//         });
//     } else {
//       // getFloorOpt(newV);
//     }
//   }
// );

setColumns();
</script>

<style scoped lang="scss">
.InspectAddPlanTable {
  height: 65vh;
  row-gap: 20px;
  padding: 10px;
}

.uncheckAllTable {
  height: 100%;
  :deep(.n-data-table-th--selection .n-checkbox) {
    display: none !important;
  }
}
</style>
