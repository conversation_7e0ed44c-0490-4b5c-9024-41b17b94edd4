<template>
  <n-form inline size="small" label-placement="left">
    <n-form-item label="楼栋:">
      <n-select
        class="!w-[260px]"
        placeholder="请选择楼栋"
        v-model:value="buildId"
        :options="buildOpt"
        label-field="buildingName"
        value-field="buildingId"
        @update:value="buildChange"
      />
    </n-form-item>
    <n-form-item label="楼层:">
      <n-select
        class="!w-[260px]"
        multiple
        v-model:value="floorId"
        placeholder="请选择楼层"
        :options="floorOpt"
        label-field="floorName"
        value-field="floorId"
        clearable
        @update:value="floorChange"
      />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import { ref, computed, watch, toRaw, nextTick } from 'vue';
import { getBuildingListByUnitId, getFloorList } from '@/views/inspect-plan/fetchData';
import { PlanService } from '@/views/inspect-plan/updatePlan/planService.ts';
import { useActionWarning } from '@/common/hooks/comfirm.ts';
import { IBuildingItem } from '@/views/inspect-plan/type';

const buildOpt = ref<IBuildingItem[]>([]);
const floorOpt = ref([]);

const buildId = PlanService.curBuildId;
const floorId = PlanService.curFloorId;

const getBuildOpt = (unitId: string) => {
  getBuildingListByUnitId(unitId).then((res: any) => {
    if (res.code === 'success') {
      const _data = res.data || [];
      buildOpt.value = _data;
      if (buildOpt.value.length > 0 && !PlanService.curBuildId.value) {
        PlanService.curBuildId.value = _data[0].buildingId;
      }

      getFloorOpt(PlanService.curBuildId.value || '');
    }
  });
};

const getFloorOpt = (buildId: string) => {
  const unitId = PlanService.curTreeNode.value?.attributes.erecordUnitId || '';
  getFloorList({
    unitId,
    buildId,
  }).then((res: any) => {
    if (res.code === 'success') {
      const _data = res.data || [];
      floorOpt.value = _data;

      emits('search', {
        deptId: PlanService.curTreeNode.value?.id,
        buildingId: buildId,
        floorId: null,
      });
    }
  });
};

const emits = defineEmits(['search']);

const buildChange = (value: string[]) => {
  console.log(PlanService.inspectDevices.value);
};
const floorChange = (value: string[]) => {
  const _floorIds = value.join(',');
  emits('search', {
    deptId: PlanService.curTreeNode.value?.id,
    buildingId: buildId.value,
    floorId: _floorIds,
  });
};

let cancelTag = false;
watch(
  () => buildId.value,
  (newV: string | null, oldV: string | null) => {
    if (!newV || cancelTag) {
      cancelTag = false;
      return;
    }
    PlanService.curFloorId.value = null;
    if (PlanService.inspectDevices.value.length > 0) {
      useActionWarning({ msg: '当前填写的信息将不会保存', negaT: '取消' })
        .then(() => {
          cancelTag = false;
          PlanService.inspectDevices.value = [];
          getFloorOpt(newV);
        })
        .catch(() => {
          cancelTag = true;
          buildId.value = oldV;
        });
    } else {
      getFloorOpt(newV);
    }
  }
);

watch(
  () => PlanService.curTreeNode.value,
  (newV: any, oldV: any) => {
    if (!newV) return;
    if (newV.id !== oldV?.id) getBuildOpt(newV.attributes.erecordUnitId);
  },
  {
    immediate: true,
  }
);

// getBuildOpt();

defineOptions({ name: 'SelectPointTableFilter' });
</script>

<style scoped lang="scss"></style>
