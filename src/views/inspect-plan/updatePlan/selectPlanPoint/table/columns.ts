import { DataTableColumn } from 'naive-ui';
import { PlanService } from '@/views/inspect-plan/updatePlan/planService';

export const cols: DataTableColumn[] = [
  {
    type: 'selection',
    width: 55,
  },
  {
    title: '序号',
    key: 'index',
    width: 65,
    render: (row: any, _: number) => {
      const _inspectDevices = JSON.parse(JSON.stringify(PlanService.inspectDevices.value));
      const _selectedDeviceIndex = _inspectDevices.findIndex((item: any) => {
        return item.deviceId === row.deviceId;
      });
      return _selectedDeviceIndex > -1 ? _selectedDeviceIndex + 1 : '--';
    },
  },
  {
    title: '楼层',
    key: 'floorName',
    width: 150,
  },
  {
    title: '设备位置',
    key: 'deviceAddress',
    ellipsis: {
      tooltip: true,
    },
  },
];
