<template>
  <div style="height: 65vh; margin: 0 auto; padding-top: 5%">
    <n-form
      ref="formRef"
      :model="formModel"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <n-form-item label="所属单位" path="deptId">
        <n-tree-select
          cascade
          :disabled="isEdit"
          checkable
          placeholder="请选择所属单位"
          key-field="id"
          label-field="text"
          v-model:value="formModel.deptId"
          :options="deptOptions"
          :render-switcher-icon="renderLabelIcon"
          @update:value="deptHandle"
        />
      </n-form-item>
      <n-form-item label="计划名称" path="planName">
        <n-input v-model:value="formModel.planName" placeholder="请输入计划名称" maxlength="50" />
      </n-form-item>
      <n-form-item label="计划周期" path="planStartDate">
        <n-date-picker
          placeholder="请选择计划周期"
          v-model:value="rangeDate"
          type="daterange"
          clearable
          @update:value="dateRangeChange"
          :is-date-disabled="disablePreviousDate"
        />
      </n-form-item>
      <n-form-item label="巡检频率" path="planFrequency">
        <n-input-group>
          <n-input-group-label>每</n-input-group-label>
          <n-input-number
            :style="{ width: '80px' }"
            v-model:value="formModel.planFrequency"
            :show-button="false"
            :min="1"
            :max="9999"
            :precision="0"
          />
          <n-select
            :style="{ width: '80px' }"
            v-model:value="formModel.planFrequencyUnit"
            :options="planFrequencyUnitOpt"
          />
        </n-input-group>
      </n-form-item>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, inject, toRaw } from 'vue';
import { useRoute } from 'vue-router';
import type { FormInst, CascaderOption } from 'naive-ui';
import { IActionData, IPlanBaseInfo } from '@/views/inspect-plan/type';
import { dayjs } from '@/utils/dayjs';
import { AnFilledCaretRight } from '@kalimahapps/vue-icons';
import { planDetailApi, postTreeList } from '../fetchData.ts';
import { PlanService } from '@/views/inspect-plan/updatePlan/planService.ts';
import { useStore } from '@/store/index.ts';
import { findByOrgType } from '@/utils/findOrgType.ts';

defineOptions({ name: 'InspectPlanBaseForm' });

const store = useStore().userInfo;
const treeData = useStore().treeAct;
const route = useRoute();
const isEdit = !!route.query.id;

const deptOptions = ref([]);

const getTreeList = () => {
  const params = {
    needChildUnit: '1',
    needself: '1',
    orgCode: PlanService.curTreeNode.value.id,
  };
  postTreeList(params).then((res: any) => {
    deptOptions.value = res.data;
    console.log(findByOrgType(res.data, '1'), 'findByOrgType');
    PlanService.curTreeNode.value = findByOrgType(res.data, '1');
    PlanService.baseInfoForm.value.deptId = findByOrgType(res.data, '1').id;
  });
};
getTreeList();

// 表单验证
const rules = {
  deptId: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择所属单位',
  },
  planName: {
    required: true,
    trigger: ['blur'],
    message: '请输入计划名称',
  },
  planStartDate: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择计划周期',
  },
  planFrequency: {
    type: 'number',
    required: true,
    trigger: 'change',
    message: '请输入巡检频次',
  },
};

const renderLabelIcon = (info: any) => {
  if (info.option.hasChildren) {
    return h(AnFilledCaretRight);
  }
  return h('span');
};

//计划频率选项
const planFrequencyUnitOpt = [
  {
    label: '小时',
    value: 1,
  },
  {
    label: '天',
    value: 2,
  },
  {
    label: '周',
    value: 3,
  },
  {
    label: '月',
    value: 4,
  },
  {
    label: '季度',
    value: 5,
  },
  {
    label: '年',
    value: 6,
  },
];

const formRef = ref<FormInst | null>(null);
const formModel = PlanService.baseInfoForm;

// 计划周期
const rangeDate = ref<any[] | null>(
  formModel.value.planStartDate
    ? [formModel.value.planStartDate.slice(0, 10), formModel.value.planEndDate.slice(0, 10)]
    : null
);

const setRangeData = (value: any) => {
  rangeDate.value = value;
};

// 所属单位
const deptHandle = (val: string, option: any) => {
  PlanService.curTreeNode.value = option;
};

// 计划周期
const dateRangeChange = (value: number[]) => {
  if (value == null) {
    formModel.value.planStartDate = null;
    formModel.value.planEndDate = null;
    return;
  } else {
    formModel.value.planStartDate = value[0] ? dayjs(value[0]).format('YYYY-MM-DD 00:00:00') : null;
    formModel.value.planEndDate = value[1] ? dayjs(value[1]).format('YYYY-MM-DD 23:59:59') : null;
  }
};
const disablePreviousDate = (ts: number) => {
  const date = new Date();
  date.setDate(date.getDate() - 1);
  return ts < Date.parse(date.toString());
};

const validateHandle = async () => {
  try {
    await formRef.value?.validate();
    return Promise.resolve<IPlanBaseInfo>(toRaw(formModel.value));
  } catch (error) {
    return Promise.reject<any>(error);
  }
};

defineExpose({
  validateHandle,
  setRangeData,
});
</script>

<style scoped lang="scss"></style>
