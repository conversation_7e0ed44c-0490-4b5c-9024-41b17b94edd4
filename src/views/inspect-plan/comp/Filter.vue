<template>
  <div :class="$style['wrap']">
    <n-form :show-feedback="false" label-placement="left">
      <n-grid class="flex-1" :cols="18" :x-gap="30" :y-gap="20">
        <n-form-item-gi :span="4" label="计划名称:">
          <n-input placeholder="请输入计划名称" v-model:value="filterForm.planName" clearable />
        </n-form-item-gi>
        <n-form-item-gi :span="6" label="创建时间:">
          <n-date-picker class="w-full" v-model:value="filterForm.rangeDateTime" type="daterange" clearable />
        </n-form-item-gi>
        <n-form-item-gi :span="4" label="计划状态:">
          <n-select
            v-model:value="filterForm.planStatus"
            placeholder="全部"
            :options="options"
            clearable
            label-field="label"
            value-field="value"
          />
        </n-form-item-gi>
        <n-form-item-gi :span="4" class="flex justify-end">
          <n-button type="primary" @click="toUpdate()">
            <CdAdd />
            {{ ACTION_LABEL.ADD }}
          </n-button></n-form-item-gi
        >
      </n-grid>

      <div class="w-[12%] flex justify-end"></div>
    </n-form>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { formatTimestamp } from '@/utils/format.ts';
import { ACTION, ACTION_LABEL } from '../constant.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { trimObjNull } from '@/utils/obj.ts';
import { CdAdd } from '@kalimahapps/vue-icons';
import { dayjs } from '@/utils/dayjs';
import { useActionWarning } from '@/common/hooks/comfirm.ts';
import { PlanService } from '@/views/inspect-plan/updatePlan/planService.ts';

const emits = defineEmits(['action']);
const router = useRouter();
const [loading, search] = useAutoLoading(false);
const filterForm = ref(initForm());
const unitOptions = ref([]);

function initForm() {
  return {
    planName: '',
    rangeDateTime: <null | [number, number]>null,
    planStatus: null,
  };
}
const toUpdate = () => {
  router.push({
    name: 'inspectPlanUpdate',
    query: {
      unitId: PlanService.curTreeNode.value?.id,
    },
  });
};

function reset() {
  filterForm.value = initForm();
}
const options = ref([
  { label: '待开始', value: 0 },
  { label: '进行中', value: 1 },
  {
    label: '已完成',
    value: 2,
  },
  { label: '已停用', value: 3 },
]);

function getFilterForm() {
  const { rangeDateTime } = filterForm.value;

  // 检查 rangeDateTime 是否是类似 {0: timestamp, 1: timestamp} 的对象
  const hasValidTimestamps = rangeDateTime && rangeDateTime[0] !== undefined && rangeDateTime[1] !== undefined;

  let startDate = null;
  let endDate = null;

  if (hasValidTimestamps) {
    const startDayjs = dayjs(Number(rangeDateTime[0]));
    const endDayjs = dayjs(Number(rangeDateTime[1]));

    if (startDayjs.isValid()) {
      startDate = startDayjs.startOf('day').format('YYYY-MM-DD HH:mm:ss');
    }

    if (endDayjs.isValid()) {
      endDate = endDayjs.endOf('day').format('YYYY-MM-DD HH:mm:ss');
    }
  }

  return trimObjNull(
    Object.assign({}, filterForm.value, {
      rangeDateTime: null,
      startDate,
      endDate,
    })
  );
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}
// onMounted(() => {
//   doHandle(ACTION.SEARCH);
// });

watch(filterForm.value, (val) => {
  console.log(val, 'val');
  doHandle(ACTION.SEARCH);
});

defineExpose({
  reset,
  emit(data: any, source?: string) {
    console.log(data, 'propsDataaaaa');
    // 来自卡片
    if (source === 'InfoCardTask') {
      filterForm.value.planStatus = data.planStatus;
      return;
    }
  },
});
defineOptions({ name: 'VideoEquiFilter' });
</script>

<style module lang="scss">
.wrap {
  width: 100%;
  padding: 20px 0;
}
</style>
