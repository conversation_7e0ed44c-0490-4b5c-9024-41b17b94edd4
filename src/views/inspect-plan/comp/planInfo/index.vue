<template>
  <div>
    <ComHeaderD title="计划基本信息" />
    <div :class="$style.baseInfo">
      <!-- 原有基本信息保持不变 -->
      <span>所属单位：{{ detailInfo?.deptName || '--' }}</span>
      <span>计划名称：{{ detailInfo?.planName || '--' }}</span>
      <span
        >计划周期：{{
          detailInfo?.planStartDate
            ? `${dayjs(detailInfo.planStartDate).format('YYYY-MM-DD')} ~ ${dayjs(detailInfo.planEndDate).format('YYYY-MM-DD')}`
            : '--'
        }}</span
      >
      <span
        >巡检频次：{{
          detailInfo?.planFrequency
            ? ` 每隔${detailInfo?.planFrequency}${
                detailInfo?.planFrequencyUnit == 1
                  ? '小时'
                  : detailInfo?.planFrequencyUnit == 2
                    ? '天'
                    : detailInfo?.planFrequencyUnit == 3
                      ? '周'
                      : detailInfo?.planFrequencyUnit == 4
                        ? '月'
                        : detailInfo?.planFrequencyUnit == 5
                          ? '季度'
                          : '年'
              }`
            : '--'
        }}</span
      >
      <span>创建人员：{{ detailInfo?.createdByName || '--' }}</span>
      <span>创建时间：{{ detailInfo?.createTime || '--' }}</span>
    </div>
    <div class="!mt-[20px] relative">
      <ComHeaderD title="计划巡检点位" />
      <div :class="$style.InspectEventTable" class="com-g-row-1a">
        <InspectList />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, provide, Ref } from 'vue';
import { useRoute } from 'vue-router';
import { planDetailApi } from '@/views/inspect-plan/fetchData.ts';
import InspectList from './inspectList.vue';
import { PROVIDE_KEY } from './constant';
import dayjs from 'dayjs';
import ComHeaderD from '@/components/header/ComHeaderD.vue';

const route = useRoute();

const detailInfo = ref<any>({});

// provide
provide<Ref<any>>(PROVIDE_KEY.DETAILDATA, detailInfo);

const getDetailData = async () => {
  let res = await planDetailApi(route.query.id as string);
  detailInfo.value = res.data;
};
getDetailData();

defineOptions({ name: 'InspectPlanPlanInfo' });
</script>

<style module lang="scss">
.baseInfo {
  height: 120px;
  border: 1px solid #0081ff;
  display: grid;
  grid-template-columns: repeat(4, 2fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 16px 0;
  align-items: center;
  padding: 20px 30px;

  > span {
    font-size: 14px;
    white-space: nowrap;
  }
}
.InspectEventTable {
  height: 53vh;
  row-gap: 20px;
  padding: 10px;
}
</style>
