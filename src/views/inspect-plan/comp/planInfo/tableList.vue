<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
  <VideoDia :device-id="dId" v-model:show="showVideoDia" />
</template>

<script setup lang="ts">
import { ref, h } from 'vue';
import { useRoute } from 'vue-router';
import { getInspectionPointListAPI } from '@/views/inspect-plan/fetchData.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { NButton } from 'naive-ui';
import { cols } from '../PonitColumns';
import { getVideoDeviceInfoAPI } from '../../fetchData.ts';
import VideoDia from '@/views/inspect-com/InspectVideoDia.vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';

defineOptions({ name: 'InspectPlanTbale' });

const route = useRoute();

const columns = ref<any>([]);
const [loading, search] = useAutoLoading(true);
const { pagination, updateTotal } = useNaivePagination(getTableData);

function setColumns() {
  columns.value.push(...cols);
  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

const dId = ref({});
const showVideoDia = ref(false);

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [];
  acList.push([
    h(
      NButton,
      {
        color: 'rgba(82,124,255,0.1)',
        size: 'small',

        class: 'com-action-button1',
        onClick: () => {
          dId.value = row.deviceId;
          showVideoDia.value = true;
        },
      },
      { default: () => '查看视频' }
    ),
  ]);

  return acList;
}

setColumns();

const tableData = ref([]);
function getTableData() {
  const params = {
    planId: route.query.id,
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
  };

  search(getInspectionPointListAPI(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}
getTableData();
</script>

<style scoped lang="scss"></style>
