<template>
  <FloorGis
    v-if="buildId"
    :type="gisType"
    :build-id="buildId"
    :floor-id="floorId"
    :default-inspect-list="inspectList"
  />
</template>

<script setup lang="ts">
import { ref, computed, inject, Ref } from 'vue';
import { PROVIDE_KEY } from './constant';
import FloorGis from '@/gis-floor/floorGis.vue';
import { EGisType } from '@/gis-floor/constant';

defineOptions({ name: 'InspectPlanGis' });

const gisType = EGisType.INSPECTDETAIL;

const detailInfo = inject(PROVIDE_KEY.DETAILDATA) as Ref<any>;

// deviceSort
const inspectList = computed<any[]>(() => {
  const _list = detailInfo.value?.relVideos || [];
  _list.sort((a: any, b: any) => a.deviceSort - b.deviceSort);
  return _list;
});

const buildId = computed(() => inspectList.value[0]?.buildingId || '');
const floorId = computed(() => inspectList.value[0]?.floorId || '');
</script>

<style scoped lang="scss"></style>
