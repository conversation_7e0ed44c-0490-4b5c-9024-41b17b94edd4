<template>
  <div class="InspectPlanList com-g-row-1a">
    <ComBoxGroup v-if="dataList.length > 0">
      <ComBoxA
        :status-label="getStatusLabel(item.planStatus)"
        v-for="item in dataList"
        :key="item.id"
        :status-bg-class="getStatusBgClass(item.planStatus)"
      >
        <ItemComp :data="item">
          <template #action-buttons>
            <n-button
              type="primary"
              :disabled="item.planStatus == 1"
              size="small"
              v-if="item.planStatus == 0"
              @click="del(item)"
              >{{ ACTION_LABEL.DEL }}</n-button
            >
            <n-button
              type="primary"
              :disabled="item.planStatus == 1 || item.planStatus == 2"
              size="small"
              @click="toUpdate(item)"
              >{{ ACTION_LABEL.EDIT }}</n-button
            >
            <n-button
              type="primary"
              size="small"
              :disabled="item.planStatus == 2"
              @click="
                opeMore(
                  item.planStatus == 0 || item.planStatus == 2 || item.planStatus == 3
                    ? ACTION.USING
                    : ACTION.STOPUSING,
                  item
                )
              "
            >
              {{
                item.planStatus == 0 || item.planStatus == 2 || item.planStatus == 3
                  ? ACTION_LABEL.USING
                  : ACTION_LABEL.STOPUSING
              }}
            </n-button>
            <n-button type="primary" size="small" @click="handleToDetail(item)">{{ ACTION_LABEL.DETAILS }}</n-button>
          </template>
        </ItemComp>
      </ComBoxA>
    </ComBoxGroup>
    <Empty title="" v-else />
    <PaginationComp class="justify-end items-center" />
  </div>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../constant.ts';
import { IObj } from '@/types';
import { IPageItem } from '../type.ts';

import { getPlanListApi, planUpStatusApi, planDetailApi, postTreeList, deleteAPI } from '../fetchData.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination, useNPaginationComp } from '@/common/hooks/useNaivePagination.ts';
import { ref } from 'vue';
import ComBoxA from '@/components/box/ComBoxA.vue';
import ComBoxGroup from '@/components/box/ComBoxGroup.vue';
import Empty from '@/components/empty/index.vue';
import ItemComp from './Item.vue';
import bg from '@/assets/bg.png';
import { useRouter } from 'vue-router';
import { useStore } from '@/store';
import { PlanService } from '@/views/inspect-plan/updatePlan/planService.ts';
import { storeToRefs } from 'pinia';
import { useActionWarning, useActionComfirm } from '@/common/hooks/comfirm.ts';
import { $toast } from '@/common/shareContext/useToastCtx';

const store = useStore();
const { treeAct } = storeToRefs(store);
const emits = defineEmits(['action']);
const router = useRouter();

const [loading, search] = useAutoLoading(true);
const dataList = ref<IPageItem[]>([]);
const paginationOpt = useNaivePagination(() => getData(), { pageSizes: [6, 12, 18] });
const PaginationComp = useNPaginationComp(paginationOpt.pagination);
paginationOpt.pagination.pageSize = 6;

let filterData: IObj<any> = {}; // 搜索条件

const toUpdate = (data: object) => {
  router.push({
    name: 'inspectPlanUpdate',
    query: {
      id: data.id,
      unitId: data.deptId,
    },
  });
};

function getData() {
  const params = {
    // deptId: treeAct.value?.id,
    // levelCode: treeAct.value?.levelCode,
    pageNo: paginationOpt.pagination.page,
    pageSize: paginationOpt.pagination.pageSize,
    ...filterData,
  };

  search(getPlanListApi(params)).then((res) => {
    dataList.value = res.data.rows || [];
    paginationOpt.updateTotal(res.data.total || 0);
  });
}
function opeMore(action: string, row: any) {
  switch (action) {
    case ACTION.USING:
      useActionWarning({ msg: '确定启用吗？', negaT: '取消' }).then(() => {
        planUpStatus(row.id, 1);
      });
      break;
    case ACTION.STOPUSING:
      console.log('停用');
      useActionWarning({ msg: '确定停用吗？', negaT: '取消' }).then(() => {
        planUpStatus(row.id, 2);
      });

      break;
  }
}

function del(item) {
  // useActionWarning({ msg: '确定删除吗？', negaT: '取消' }).then(() => {

  // });
  useActionComfirm({ msg: '确定删除吗？', negaT: '取消' }).then(() => {
    deleteAPI({ id: item.id }).then((res) => {
      $toast.success('删除成功');
      getData();
    });
  });
}

//启用、停用
function planUpStatus(id: string, planStatus: number) {
  planUpStatusApi({ id, planStatus })
    .then((res) => {
      emits('action', { action: ACTION.TREECHANGE, data: {} });
    })
    .finally(() => {});
}

function getDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  paginationOpt.pagination.page = 1;
  getData();
}

const STATUS_CONFIG = {
  0: { label: '待开始', bgClass: 'purple' },
  1: { label: '进行中', bgClass: 'blue' },
  2: { label: '已完成', bgClass: 'green' },
  3: { label: '已停用', bgClass: 'gray' },
} as const;

const getStatusLabel = (state: number): string => {
  return STATUS_CONFIG[state as keyof typeof STATUS_CONFIG]?.label || '未知状态';
};

const getStatusBgClass = (state: number): string => {
  return STATUS_CONFIG[state as keyof typeof STATUS_CONFIG]?.bgClass || 'gray';
};
function handleToDetail(obj: object) {
  router.push({ name: 'inspectPlanDetail', query: { id: obj.id, unitId: obj.deptId } });
}

defineExpose({
  getDataWrap,
  getData,
});

defineOptions({ name: 'InspectPlanList' });
</script>

<style scoped lang="scss">
.InspectPlanList {
  row-gap: 20px;
  padding: 10px;
}
.n-button {
  margin-right: 5px;
}
.selectinfo_btn {
  margin-left: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 16px 5px 16px;
  background: linear-gradient(180deg, #0d1e3b 0%, #2251a1 100%);
  border-radius: 2px;
  border: 1px solid #3371dc;

  &:hover {
    /* 添加悬停效果 */
    background: linear-gradient(180deg, #2251a1 0%, #0d1e3b 100%); /* 反转渐变色 */
    box-shadow: 0 0 5px rgba(51, 113, 220, 0.8); /* 添加发光效果 */
  }
}
</style>
