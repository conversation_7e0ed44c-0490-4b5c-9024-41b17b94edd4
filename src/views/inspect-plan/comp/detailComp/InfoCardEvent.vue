<template>
  <div :class="$style.InspectTaskInfoCardTask">
    <div
      v-for="(item, index) of [
        { label: '异常事件数(起)', value: cardList?.abnormalTotal || 0, disposeStatus: null },
        { label: '已处置(起)', value: cardList?.disposedNum || 0, disposeStatus: 1 },
        { label: '待处置(起)', value: cardList?.undisposedNum || 0, disposeStatus: 0 },
        { label: '处置中(起)', value: cardList?.disposingNum || 0, disposeStatus: 2 },
      ]"
      :key="item.label"
      :class="[$style.card, $style[`bg-${index + 1}`], curCardIndex === index && $style.active]"
      @click="handleCardClick(item, index)"
    >
      <div>{{ item.value }}</div>
      <div>{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { getDisposeOverViewStatisticsAPI } from '../../fetchData.ts';

const emits = defineEmits(['change']);

const route = useRoute();
const cardList = ref({});

// 当前卡片索引
const curCardIndex = ref<number | null>(0);
function handleCardClick(item: any, index: number) {
  if (curCardIndex.value === index) {
    curCardIndex.value = null;
  } else {
    curCardIndex.value = index;
  }

  emits('change', { disposeStatus: curCardIndex.value === null ? null : item.disposeStatus });
}

const getCardList = async () => {
  let res = await getDisposeOverViewStatisticsAPI({ planId: route.query.id });
  cardList.value = res.data;
};
getCardList();
const cards = ref();

function getData() {
  // todo
}

// init
getData();

defineOptions({ name: 'InspectTaskInfoCardTask' });
</script>

<style module lang="scss">
.InspectTaskInfoCardTask {
  display: grid;
  grid-template-columns: repeat(4, 290px);
  gap: 10px 20px;
  justify-content: center;

  .card {
    width: 290px;
    height: 74px;
    display: flex;
    flex-direction: column;
    padding: 5px 20px;
    color: #fff;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    border: 2px solid transparent;
    border-radius: 2px;
    cursor: pointer;

    &.bg-1 {
      background-image: url('./assets/bg1.png');
      &.active {
        border: 2px solid #ff67b0;
      }
    }
    &.bg-2 {
      background-image: url('./assets/bg2.png');
      &.active {
        border: 2px solid #00db42;
      }
    }
    &.bg-3 {
      background-image: url('./assets/bg3.png');
      &.active {
        border: 2px solid #b900b3;
      }
    }
    &.bg-4 {
      background-image: url('./assets/bg4.png');
      &.active {
        border: 2px solid #299fff;
      }
    }
    > div:nth-child(1) {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }

    > div:nth-child(2) {
      font-size: 14px;
      font-weight: bold;
      opacity: 0.9;
    }
  }
}
</style>
