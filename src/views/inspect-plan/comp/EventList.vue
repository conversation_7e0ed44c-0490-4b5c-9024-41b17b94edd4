<template>
  <div class="com-g-row-aa1">
    <InfoCardTask @change="handleInfoCardTaskChange" />
    <Filter ref="filterCompRef" @action="actionFn" />
    <Table class="com-table-container" ref="listCompRef" @action="actionFn" />

    <DeviceLocation v-model:show="showDrawer" :device-list="deviceList" />
  </div>
</template>

<script setup lang="ts">
import { ACTION, PROVIDE_KEY } from '../constant.ts';
import { ref, provide, Ref } from 'vue';
import Filter from './detailComp/Filter.vue';
import InfoCardTask from './detailComp/InfoCardEvent.vue';
import Table from './detailComp/Table.vue';
import { IActionData } from '@/views/inspect-plan/constant.ts';
import type { IObj } from '@/types';
import DeviceLocation from '@/views/inspect-com/DeviceLocation.vue';
import { getDeviceInfo } from '@/views/inspect-plan/fetchData.ts';

const currentAction = ref<IActionData>({ action: ACTION.SEARCH, data: {} });
const filterCompRef = ref();
const listCompRef = ref();

const showDrawer = ref(false);
const deviceList = ref<any[]>([]);

async function viewDeviceLocation(id: string) {
  try {
    const res: any = await getDeviceInfo(id);

    if (res.code === 'success') {
      const data = res.data;
      deviceList.value = [
        {
          deviceId: data.deviceId,
          deviceNum: data.deviceNum,
          deviceTypeId: data.deviceTypeId,
          deviceAddress: data.deviceAddress,
          buildingId: data.buildingId,
          floorId: data.floorId,
          mapX: data.mapX,
          mapY: data.mapY,
          mapZ: data.mapZ,
          latitude: data.latitude,
          longitude: data.longitude,
        },
      ];
      showDrawer.value = true;
    }
  } catch (error) {}
}

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  currentAction.value = val;

  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  }

  if (val.action === ACTION.DEVICELOC) {
    viewDeviceLocation(val.data.deviceId);
  }
}

function handleInfoCardTaskChange(data: IObj<any>) {
  filterCompRef.value?.emit(data, 'InfoCardTask');
}

function handleSearch(data?: IObj<any>) {
  if (data) {
    listCompRef.value?.getDataWrap(data);
  } else {
    listCompRef.value?.getData();
  }
}

defineOptions({ name: 'InspectPlanEvent' });
</script>

<style module lang="scss"></style>
