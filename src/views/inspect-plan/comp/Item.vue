<template>
  <div class="com-g-row-a1">
    <div class="head">
      <n-ellipsis :class="$style.title">
        {{ data.planName || '--' }}
      </n-ellipsis>
      <p>创建时间：{{ data.createTime || '--' }}</p>
    </div>
    <div class="card">
      <div>
        <p>
          <span>{{ data.inspectionPointCount || 0 }}</span
          >个
        </p>
        <p class="card_p">巡检点位</p>
      </div>
      <div>
        <p>
          <span>{{ data.taskExecuteCount || 0 }}</span
          >次
        </p>
        <p class="card_p">任务执行</p>
      </div>
      <div>
        <p>
          <span>{{ data.abnormalCount || 0 }}</span
          >次
        </p>
        <p class="card_p2">发现异常</p>
      </div>
      <div>
        <p>
          <span>{{ data.planFrequency || 0 }}</span
          >{{
            data.planFrequencyUnit == 1
              ? '小时'
              : data.planFrequencyUnit == 2
                ? '天'
                : data.planFrequencyUnit == 3
                  ? '周'
                  : data.planFrequencyUnit == 4
                    ? '月'
                    : data.planFrequencyUnit == 5
                      ? '季度'
                      : '年'
          }}/次
        </p>
        <p class="card_p">巡检频率</p>
      </div>
    </div>
    <div class="com-g-row-a1" v-show="data.planStatus !== 0">
      <div class="content">
        <n-image
          :src="getFullThumbnailUrl(data.inspectionPicture)"
          alt=""
          :preview-src="getFullFileUrl(data.inspectionPicture)"
          v-if="data.inspectionPicture && data.inspectionPicture !== ''"
        />
        <n-image :src="itemNoPic" alt="" preview-disabled class="pointer-events-none" v-else />

        <div :title="data.lastTaskExecuteTime">
          <p class="text-ellipsis">最近一次任务执行时间：{{ data.lastTaskExecuteTime || '--' }}</p>
          <p>
            巡检结果：<span style="color: red" v-if="data.inspectionResult == '异常'">{{ data.inspectionResult }}</span>
            <span style="color: green" v-else-if="data.inspectionResult == '正常'">{{ data.inspectionResult }}</span>
            <span v-else>--</span>
          </p>
          <p>
            异常情况：<span style="color: red">{{ data.deviceExceptionCount || 0 }}处</span>
          </p>
        </div>
      </div>
    </div>
    <div v-show="data.planStatus == 0" class="flex">
      <img src="../assets/unStart2.png" />
      <p
        style="color: #999; display: flex; flex-direction: column; align-items: center; justify-content: center"
        class="!ml-[10px]"
      >
        任务开始后自动生成任务缩略图
      </p>
    </div>
    <div class="flex justify-end items-center !mt-[5px]">
      <slot name="action-buttons"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import itemNoPic from '@/views/inspect-task/assets/item-no-pic.png';
import { getFullFileUrl, getFullThumbnailUrl } from '@/utils/getImgUrl';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

defineOptions({ name: 'InspectTaskListItem' });
</script>

<style scoped lang="scss">
.head {
  p {
    font-size: 14px;
    color: #acb9d0;
  }
}
.card {
  width: 100%;
  height: 78px;
  margin-bottom: 5px;
  display: flex;
  gap: 2px;
  div {
    height: 78px;
    display: flex;
    flex: 1;
    padding: 0 10px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: rgba(97, 125, 167, 0.08);
    span {
      font-size: 24px;
    }
    .card_p {
      width: 68px;
      height: 24px;
      margin-top: 5px;
      line-height: 24px;
      // background: rgba(130, 150, 217, 0.4);
      border-radius: 2px;
    }
    .card_p2 {
      width: 68px;
      height: 24px;
      line-height: 24px;
      margin-top: 5px;
      // background: rgba(255, 72, 72, 0.4);
      border-radius: 2px;
    }
  }
}
.content {
  width: 100%;
  height: 100px;

  display: flex;
  font-size: 12px;
  margin-bottom: 5px;
  div:nth-child(2) {
    margin-left: 15px;
    p {
      margin-bottom: 10px;
    }
    .text-ellipsis {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 200px;
    }
  }
}
</style>

<style module lang="scss">
.title {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0;
  max-width: 100%;
}
</style>
