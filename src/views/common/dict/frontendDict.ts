/**
 * 前端静态字典
 */
import { IDict } from '@/types';
import { useState } from '@/common/hooks/useState.ts';

export const frontendDict = {
  // 巡检状态
  useXjzt() {
    const [opt, setOpt] = useState<IDict[]>([
      { dictLabel: '待巡检', dictValue: '0' },
      { dictLabel: '已巡检', dictValue: '1' },
    ]);

    return { xjztOpt: opt };
  },

  // 任务状态
  useRwzt() {
    const [opt, setOpt] = useState<IDict[]>([
      { dictLabel: '待开始', dictValue: '0' },
      { dictLabel: '未执行', dictValue: '1' },
      { dictLabel: '进行中', dictValue: '2' },
      { dictLabel: '已完成', dictValue: '3' },
    ]);

    return { rwztOpt: opt };
  },

  // 巡检视频结果
  useXjspjg(filterValues?: string[]) {
    const [opt, setOpt] = useState<IDict[]>([
      { dictLabel: '正常', dictValue: '0' },
      { dictLabel: '待巡检', dictValue: '1' },
      { dictLabel: '异常', dictValue: '2' },
    ]);

    if (Array.isArray(filterValues) && filterValues.length > 0) {
      const filtered = opt.value.filter((item: IDict) => filterValues.includes(item.dictValue));
      setOpt(filtered);
    }

    return { xjspjgOpt: opt };
  },

  // 在线状态
  useOnlineState() {
    const [opt, setOpt] = useState<IDict[]>([
      { dictLabel: '在线', dictValue: '0' },
      { dictLabel: '离线', dictValue: '1' },
    ]);

    return { onlineStateOpt: opt };
  },
};
