<template>
  <div class="com-g-row-a1">
    <com-bread :data="breadData" />
    <div class="com-g-col-a1">
      <ComOrgTreeWrap @change="handleChange" />
      <div class="com-g-row-aa1">
        <TopCards class="h-full" />
        <TimeRangeSelector class="h-full" @time-change="handleTimeChange" />
        <CenterCards class="h-full" />
        <div></div>
        <ChartCard type="locationTop5" class="h-full" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import ComOrgTreeWrap from '@/components/tree/OrgTreeWrap.vue';
import CenterCards from './comp/centerCards.vue';
import ChartCard from './comp/ChartCard.vue';
import TopCards from './comp/TopCards.vue';
import TimeRangeSelector from './comp/TimeRangeSelector.vue';
import { IBreadData } from '@/components/breadcrumb/type.ts';

const breadData = ref<IBreadData[]>([{ name: '视频智能巡检' }, { name: '数据分析' }]);

function handleChange(val: any) {
  console.log('组织树选择变化:', val);
  // 这里可以处理组织树选择变化的逻辑
}

function handleTimeChange(timeRange: any) {
  console.log('时间范围变化:', timeRange);
  // 这里可以处理时间范围变化的逻辑，比如更新图表数据
}

defineOptions({ name: 'DataAnalysisIndex' });
</script>

<style scoped lang="scss">
.com-g-row-aa1 {
  grid-template-rows: 19% 60px 1fr 20px 1fr;
}
</style>
