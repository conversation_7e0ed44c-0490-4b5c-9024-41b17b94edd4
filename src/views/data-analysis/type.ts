/**
 * 数据分析页面类型定义
 */

// 统计卡片数据类型
export interface StatCardData {
  id: string;
  title: string;
  value: number | string;
  unit?: string;
  icon?: string;
  color?: string;
}

// 饼图数据类型
export interface PieChartData {
  name: string;
  value: number;
  color?: string;
}

// 柱状图数据类型
export interface BarChartData {
  name: string;
  value: number;
  category?: string;
}

// 趋势图数据类型
export interface TrendChartData {
  date: string;
  value: number;
  category?: string;
}

// 组织树由统一的 ComOrgTreeWrap 组件处理，使用其自带的类型定义

// 异常事件分布数据
export interface AbnormalEventDistribution {
  type: string;
  count: number;
  percentage: number;
}

// 事件处置状态
export interface EventProcessStatus {
  status: string;
  count: number;
  color: string;
}

// TOP5数据类型
export interface Top5Data {
  rank: number;
  name: string;
  value: number;
}

// 页面主要数据接口
export interface DataAnalysisPageData {
  // 顶部统计数据
  totalPlans: number;
  totalTasks: number;
  abnormalEvents: number;
  aiPredictionModules: number;

  // 处理状态统计
  processedCount: number;
  processingCount: number;
  pendingCount: number;

  // 图表数据
  eventDistribution: PieChartData[];
  eventProcessing: EventProcessStatus[];
  taskTrend: TrendChartData[];
  highFrequencyEvents: PieChartData[];
  locationTop5: Top5Data[];
  partTop5: Top5Data[];
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 查询参数类型
export interface QueryParams {
  orgId?: string;
  startDate?: string;
  endDate?: string;
  eventType?: string;
  status?: string;
}
