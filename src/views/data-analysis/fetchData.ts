/**
 * 数据分析页面数据获取服务
 */

import { http } from '@tanzerfe/http';
import type { DataAnalysisPageData, ApiResponse, QueryParams, PieChartData, Top5Data } from './type';

/**
 * 获取页面主要数据
 */
export const getDataAnalysisData = async (params?: QueryParams): Promise<DataAnalysisPageData> => {
  try {
    const response = await http.get<ApiResponse<DataAnalysisPageData>>('/data-analysis/overview', {
      params,
    });

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取数据分析数据失败:', error);
    throw error;
  }
};

// 组织树数据由统一的 ComOrgTreeWrap 组件处理，无需自定义获取函数

/**
 * 获取异常事件分布数据
 */
export const getEventDistributionData = async (params?: QueryParams): Promise<PieChartData[]> => {
  try {
    const response = await http.get<ApiResponse<PieChartData[]>>('/data-analysis/event-distribution', {
      params,
    });

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '获取事件分布数据失败');
    }
  } catch (error) {
    console.error('获取事件分布数据失败:', error);
    throw error;
  }
};

/**
 * 获取高频事件位置TOP5数据
 */
export const getLocationTop5Data = async (params?: QueryParams): Promise<Top5Data[]> => {
  try {
    const response = await http.get<ApiResponse<Top5Data[]>>('/data-analysis/location-top5', {
      params,
    });

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '获取位置TOP5数据失败');
    }
  } catch (error) {
    console.error('获取位置TOP5数据失败:', error);
    throw error;
  }
};

/**
 * 获取高频事件部位TOP5数据
 */
export const getPartTop5Data = async (params?: QueryParams): Promise<Top5Data[]> => {
  try {
    const response = await http.get<ApiResponse<Top5Data[]>>('/data-analysis/part-top5', {
      params,
    });

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '获取部位TOP5数据失败');
    }
  } catch (error) {
    console.error('获取部位TOP5数据失败:', error);
    throw error;
  }
};

/**
 * 获取任务趋势数据
 */
export const getTaskTrendData = async (params?: QueryParams) => {
  try {
    const response = await http.get('/data-analysis/task-trend', {
      params,
    });

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || '获取任务趋势数据失败');
    }
  } catch (error) {
    console.error('获取任务趋势数据失败:', error);
    throw error;
  }
};

/**
 * 导出数据分析报告
 */
export const exportAnalysisReport = async (params?: QueryParams) => {
  try {
    const response = await http.post('/data-analysis/export', params, {
      responseType: 'blob',
    });

    return response.data;
  } catch (error) {
    console.error('导出报告失败:', error);
    throw error;
  }
};
