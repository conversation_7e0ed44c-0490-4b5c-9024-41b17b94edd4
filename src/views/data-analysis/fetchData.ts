import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import { InspectionVideoOverview, InspectionVideoDataAnalysisXYChartData } from './type';

//智能巡检计划情况
export function overView(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.dataAnalysis.intelligentOver, query);

  return $http.post<InspectionVideoOverview>(params.url, { data: { _cfg: { showTip: true }, ...params.data } });
}

//巡检高频事件位置TOP5
export function topNEventGroupByPosition(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.dataAnalysis.topNEventGroupByPosition, query);

  return $http.post<InspectionVideoDataAnalysisXYChartData[]>(params.url, {
    data: { _cfg: { showTip: true }, ...params.data },
  });
}

//巡检高频事件点位TOP5
export function topNEventGroupByVideoDevice(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.dataAnalysis.topNEventGroupByVideoDevice, query);

  return $http.post<InspectionVideoDataAnalysisXYChartData[]>(params.url, {
    data: { _cfg: { showTip: true }, ...params.data },
  });
}
