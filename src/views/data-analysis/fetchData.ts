import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IObj } from '@/types';
import { InspectionVideoOverview } from './type';

//智能巡检计划情况
export function overView(query: IObj<any>) {
  const params = api.getComParams(api.type.intelligent, api.name.dataAnalysis.intelligentOver, query);

  return $http.post<InspectionVideoOverview>(params.url, { data: { _cfg: { showTip: true }, ...params.data } });
}
