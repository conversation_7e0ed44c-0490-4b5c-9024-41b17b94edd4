<template>
  <div class="progress-container">
    <svg viewBox="0 0 100 100">
      <!-- 背景圆环 -->
      <circle class="background-circle" cx="50" cy="50" r="46" :style="{ color: '#00A0A2' }"></circle>

      <!-- 多条数据的圆环 -->
      <template v-if="Array.isArray(props.data)">
        <circle
          v-for="(item, index) in props.data"
          :key="index"
          class="progress-circle multi-progress"
          :class="`progress-${item.status || index}`"
          :style="{
            stroke: item.color,
            strokeWidth: 6,
            transformOrigin: '50% 50%',
          }"
          cx="50"
          cy="50"
          r="46"
        ></circle>
      </template>

      <!-- 单条数据的圆环（保持向后兼容） -->
      <template v-else>
        <circle
          class="progress-circle"
          :class="`progress-${props.data.status}`"
          :style="{ stroke: props.data.color }"
          cx="50"
          cy="50"
          r="45"
        ></circle>
      </template>
    </svg>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, watch } from 'vue';

const props = defineProps({
  data: {
    type: [Object, Array],
    default: () => {
      return {
        progress: 10,
        color: '#409EFF',
        className: '',
      };
    },
  },
});

// 圆环周长
const circumference = computed(() => 2 * Math.PI * 46); // 2πr，这里 r=46，与SVG中的半径保持一致

watch(
  () => props.data,
  () => {
    if (props.data) {
      setTimeout(() => {
        if (Array.isArray(props.data)) {
          const totalValue = props.data.reduce((sum, item) => sum + (item.progress || 0), 0);
          let cumulativePercentage = 0;

          props.data.forEach((item, index) => {
            const currentPercentage = totalValue > 0 ? ((item.progress || 0) / totalValue) * 100 : 0;
            setMultiProgress(currentPercentage, item.status || index, cumulativePercentage);
            cumulativePercentage += currentPercentage;
          });
        } else {
          setProgress(props.data?.progress || 0);
        }
      }, 30);
    }
  },
  { immediate: true, deep: true }
);

// 设置单条数据的进度
const setProgress = (percentage: number) => {
  nextTick(() => {
    let progressCircle = document.querySelector(`.progress-${(props.data as any).status}`);
    const offset = (circumference.value * (100 - percentage)) / 100;
    if (progressCircle?.style) {
      progressCircle.style.strokeDashoffset = offset < 0 ? 0 : offset;
    }
  });
};

// 设置多条数据的进度
const setMultiProgress = (percentage: number, statusOrIndex: number, cumulativeOffset: number) => {
  nextTick(() => {
    const progressCircle = document.querySelector(`.progress-${statusOrIndex}`) as HTMLElement;
    if (progressCircle?.style) {
      const arcLength = (circumference.value * percentage) / 100;
      const startOffsetFromTop = (circumference.value * cumulativeOffset) / 100;
      progressCircle.style.strokeDasharray = `${arcLength} ${circumference.value - arcLength}`;
      progressCircle.style.strokeDashoffset = `${circumference.value / 4 - startOffsetFromTop}`;
    }
  });
};

defineOptions({ name: 'progressCom' });
</script>
<style lang="scss" scoped>
.progress-container {
  width: 130px;
  height: 130px;
  position: absolute;
  transform: rotate(90deg);

  // 为小圆环提供不同的尺寸
  &.mini {
    width: 80px;
    height: 80px;
  }

  .svg {
    width: 100%;
    height: 100%;
  }

  .background-circle {
    fill: none;
    stroke: rgba(255, 255, 255, 0.2);
    stroke-width: 8;
  }

  .progress-circle {
    fill: none;
    stroke: #4caf50;
    stroke-width: 8;
    stroke-dasharray: 288.88;
    /* 2πr，这里 r=46 */
    stroke-dashoffset: 288.88;
    stroke-linecap: butt; // 改为平端，不要圆角
    transition: stroke-dashoffset 1s ease-in-out;

    // 多条数据的圆环样式
    &.multi-progress {
      stroke-linecap: butt; // 确保多条数据也是平端
      transition:
        stroke-dasharray 1s ease-in-out,
        stroke-dashoffset 1s ease-in-out;
    }
  }
}

// 为不同状态的圆环添加动画效果
.progress-1,
.progress-2,
.progress-3,
.progress-4 {
  transition: stroke-dashoffset 1.5s ease-in-out;
}
</style>
