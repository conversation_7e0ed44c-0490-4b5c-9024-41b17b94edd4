<template>
  <div class="chart-container w-full h-full" ref="barChart"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, markRaw, onBeforeUnmount } from 'vue';
import { useEchartsResizeObserver } from '@/common/hooks/useEchartsResizeObserver';

import * as echarts from 'echarts';

const props = defineProps({
  // 图表标题
  title: {
    type: String,
    default: '',
  },
  // 图表数据
  chartData: {
    type: Array,
    required: true,
    // 数据格式: [{name: '名称', value: 数值, color: {渐变色配置}}]
  },
  // 图例数据
  legendData: {
    type: Array,
    default: () => [],
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true,
  },
});

const barChart = ref();
const myChart = ref<any>(null);
const observer = ref<ResizeObserver>();

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

// 监听数据变化，重新渲染图表
watch(
  () => props.chartData,
  () => {
    renderChart();
  },
  { deep: true }
);

const handleResize = () => {
  myChart.value && myChart.value.resize();
};

const renderChart = () => {
  if (myChart.value) destroyEcharts();
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(barChart.value));
  const option = {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      orient: 'vertical',
      right: '10%',
      top: 'center',
      icon: 'rect',
      textStyle: {
        color: '#fff',
      },
    },
    series: [
      {
        name: '巡检高频事件位置TOP5',
        type: 'pie',
        radius: '70%',
        center: ['30%', '50%'],
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 1048, name: 'Search Engine' },
          { value: 735, name: 'Direct' },
          { value: 580, name: 'Email' },
          { value: 484, name: 'Union Ads' },
          { value: 300, name: 'Video Ads' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  myChart.value.setOption(option);

  observer.value = useEchartsResizeObserver(myChart, barChart).observer;
};

onMounted(() => {
  if (barChart.value) {
    myChart.value = echarts.init(barChart.value);
    renderChart();

    window.addEventListener('resize', handleResize);
  }
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});

onUnmounted(() => {
  if (myChart.value) {
    myChart.value.dispose();
    window.removeEventListener('resize', handleResize);
  }
});

defineOptions({ name: 'PieChartComponent' });
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
