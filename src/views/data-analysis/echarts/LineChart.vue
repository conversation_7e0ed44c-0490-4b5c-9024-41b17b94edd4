<template>
  <div class="chart-container w-full h-full" ref="barChart"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, markRaw, onBeforeUnmount } from 'vue';
import { useEchartsResizeObserver } from '@/common/hooks/useEchartsResizeObserver';

import * as echarts from 'echarts';

const props = defineProps({
  // 图表标题
  title: {
    type: String,
    default: '',
  },
  xAxis: {
    type: String,
    default: 'category',
  },
  yAxis: {
    type: String,
    default: 'value',
  },
  // 图表数据
  chartData: {
    type: Array,
    required: true,
    // 数据格式: [{name: '名称', value: 数值, color: {渐变色配置}}]
  },
  // 图例数据
  legendData: {
    type: Array,
    default: () => [],
  },
  // 是否显示图例
  showLegend: {
    type: Boolean,
    default: true,
  },
});

const barChart = ref();
const myChart = ref<any>(null);
const observer = ref<ResizeObserver>();

const handleResize = () => {
  myChart.value && myChart.value.resize();
};

const renderChart = () => {
  if (myChart.value) destroyEcharts();
  // 标记一个对象，使其永远不会再成为响应式对象
  myChart.value = markRaw(echarts.init(barChart.value));
  const option = {
    title: {
      text: '',
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      textStyle: {
        color: '#fff',
      },
      data: ['Email', 'Union Ads'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: 'Email',
        type: 'line',
        stack: 'Total',
        data: [120, 132, 101, 134, 90, 230, 210],
      },
      {
        name: 'Union Ads',
        type: 'line',
        stack: 'Total',
        data: [220, 182, 191, 234, 290, 330, 310],
      },
    ],
  };

  myChart.value.setOption(option);

  observer.value = useEchartsResizeObserver(myChart, barChart).observer;
};

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

// 监听数据变化，重新渲染图表
watch(
  () => props.chartData,
  () => {
    renderChart();
  },
  { deep: true }
);

onMounted(() => {
  if (barChart.value) {
    myChart.value = echarts.init(barChart.value);
    renderChart();

    window.addEventListener('resize', handleResize);
  }
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});

onUnmounted(() => {
  if (myChart.value) {
    myChart.value.dispose();
    window.removeEventListener('resize', handleResize);
  }
});

defineOptions({ name: 'LineChartComponent' });
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
