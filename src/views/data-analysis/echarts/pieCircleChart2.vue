<template>
  <div class="chart-container w-full h-full" ref="barChart"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, markRaw, onBeforeUnmount } from 'vue';
import { useEchartsResizeObserver } from '@/common/hooks/useEchartsResizeObserver';

import * as echarts from 'echarts';

const props = defineProps({
  // 图表标题
  title: {
    type: String,
    default: '',
  },
  // 图标位置
  center: {
    type: Boolean,
    default: false,
  },
  legend: {
    type: String,
    default: '',
  },
  color: {
    type: String,
    default: 'rgba(0, 185, 148, 1)',
  },
  // 图表数据
  chartData: {
    type: Array,
    required: true,
    // 数据格式: [{name: '名称', value: 数值, color: {渐变色配置}}]
  },
  // 图例数据
  legendData: {
    type: Array,
    default: () => [],
  },
  // 是否显示图例
  showLegend: {
    type: <PERSON>olean,
    default: true,
  },
});

const barChart = ref();
const myChart = ref<any>(null);
const observer = ref<ResizeObserver>();

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

// 监听数据变化，重新渲染图表
watch(
  () => props.chartData,
  () => {
    renderChart();
  },
  { deep: true }
);

const handleResize = () => {
  myChart.value && myChart.value.resize();
};
const circle = {
  name: '',
  type: 'pie',
  radius: ['75%', '45%'], // 内环厚度
  center: ['36%', '50%'],
  hoverAnimation: false, // 关闭悬浮放大动画
  emphasis: {
    scale: false, // 悬浮时不缩放
    itemStyle: {
      shadowBlur: 0, // 去掉阴影
    },
    label: { show: false }, // 如果不想高亮文字也可以关掉
  },
  label: { show: false, position: 'inside', color: '#fff' },
  data: [
    { value: 40, name: '进行中' },
    { value: 30, name: '已完成' },
    { value: 20, name: '待开始' },
    { value: 10, name: '未执行' },
  ],
};

const circle2 = {
  name: '',
  type: 'pie',
  clockwise: false,
  radius: ['75%', '60%'], // 内环厚度
  center: ['50%', '50%'],
  hoverAnimation: false, // 关闭悬浮放大动画
  emphasis: {
    scale: false, // 悬浮时不缩放
    itemStyle: {
      shadowBlur: 0, // 去掉阴影
    },
    label: { show: false }, // 如果不想高亮文字也可以关掉
  },
  label: { show: false, position: 'inside', color: '#fff' },
  data: [
    {
      value: 500,
      itemStyle: { color: props.color },
      label: {
        normal: {
          formatter: function (params: any) {
            return params.value + '\n\n ' + props.title;
          },
          position: 'center',
          show: true,
          textStyle: {
            fontSize: '14',
            fontWeight: 'normal',
            color: '#fff',
          },
        },
      },
    },
    {
      value: 100,
      label: {
        show: false,
      },
      itemStyle: {
        normal: {
          color: 'rgba(109, 128, 159, 0.25)',
        },
      },
    },
  ],
};

const renderChart = () => {
  if (myChart.value) destroyEcharts();
  myChart.value = markRaw(echarts.init(barChart.value));
  const option = {
    tooltip: {
      show: false,
      trigger: 'item',
      formatter: '{b} {c}',
    },
    legend: {
      show: props.legend,
      orient: 'vertical',
      right: '10%',
      top: 'center',
      icon: 'rect',
      itemHeight: 8,
      itemWidth: 8,
      textStyle: {
        fontSize: 10,
        color: '#fff',
      },
    },
    series: [
      // 1) 最内层环（四组数据）
      props.center ? circle2 : circle,
      // 2) 中间环（透明缝隙）
      {
        type: 'pie',
        radius: ['97%', '78%'], // 缝隙厚度
        center: props.center ? ['50%', '50%'] : ['36%', '50%'],
        silent: true,
        z: 5,
        label: { show: false },
        labelLine: { show: false },
        data: [
          {
            value: 100,
            itemStyle: { color: 'rgba(36, 241, 239, 0.11)' }, // 完全透明
          },
        ],
      },

      // 3) 最外层渐变环
      {
        type: 'pie',
        radius: ['98%', '93%'], // 外环（较细）
        center: props.center ? ['50%', '50%'] : ['36%', '50%'],
        z: 10,
        hoverAnimation: false,
        label: { show: false },
        labelLine: { show: false },
        data: [
          {
            value: 100,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(36, 241, 239, 1)' }, // 顶部亮色
                { offset: 1, color: 'rgba(0, 0, 0, 0)' }, // 底部暗色
              ]),
            },
          },
        ],
      },
    ],
  };

  myChart.value.setOption(option);

  observer.value = useEchartsResizeObserver(myChart, barChart).observer;
};

onMounted(() => {
  if (barChart.value) {
    myChart.value = echarts.init(barChart.value);
    renderChart();

    window.addEventListener('resize', handleResize);
  }
});

onBeforeUnmount(() => {
  destroyEcharts();
  // 在组件卸载时停止监听，避免内存泄漏
  observer.value?.disconnect();
});

onUnmounted(() => {
  if (myChart.value) {
    myChart.value.dispose();
    window.removeEventListener('resize', handleResize);
  }
});

defineOptions({ name: 'PieCircleChartComponent' });
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
