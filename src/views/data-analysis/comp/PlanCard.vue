<template>
  <div class="planCard">
    <div class="cardIcon">
      <img :src="iconSrc" alt="" />
    </div>
    <div class="cardContent">
      <div class="cardValue">
        {{ value }}<span class="cardUnit">{{ unit }}</span>
      </div>
      <div class="cardTitle">{{ title }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import plan from '../assets/plan.png';
import task from '../assets/task.png';
import ai from '../assets/ai.png';
import event from '../assets/event.png';

interface Props {
  title: string;
  value: string | number;
  unit?: string;
  icon?: string;
}

const props = withDefaults(defineProps<Props>(), {
  unit: '',
  icon: 'plan',
});

// 根据不同类型显示不同图标
const iconSrc = computed(() => {
  const iconMap = {
    plan,
    task,
    ai,
    event,
  };
  return iconMap[props.icon] || iconMap.plan;
});

defineOptions({ name: 'PlanCard' });
</script>

<style scoped lang="scss">
.planCard {
  width: 100%;
  height: 70%;
  background: rgba(97, 125, 167, 0.08);
  border-radius: 2px 2px 2px 2px;
  border: 1px solid #3c516f;
  padding: 11px;
  display: flex;
  align-items: center;
  // height: 100%;
}

.cardIcon {
  width: 41px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;

  img {
    width: 100%;
    height: 100%;
  }
}

.cardContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.cardValue {
  font-size: 24px;
  font-weight: 600;
  color: #fff;
  line-height: 1;

  .cardUnit {
    font-size: 14px;
    font-weight: 400;
    margin-left: 4px;
  }
}

.cardTitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1;
}
</style>
