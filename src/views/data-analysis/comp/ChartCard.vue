<template>
  <div class="chartCard">
    <div class="left flex-1 pt-[16px] pl-[20px] mr-[20px]">
      <ComHeaderC title="巡检高频事件次数分布" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <pieChart />
      </div>
    </div>
    <div class="center flex-1 pt-[16px] pl-[20px] mr-[20px]">
      <ComHeaderC title="巡检高频事件位置TOP5" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <BarChart :chartData="genderDistributionData" />
      </div>
    </div>
    <div class="right flex-1 pt-[16px] pl-[20px]">
      <ComHeaderC title="巡检高频事件点位TOP5" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <BarChart :chartData="genderDistributionData" xAxis="value" yAxis="category" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import pieChart from '@/views/drawing/drawingLeft/pieChart.vue';
import BarChart from '../echarts/BarChart.vue';
import { genderColorConfig } from './colorList';

interface Props {
  type?: 'eventDistribution' | 'eventProcessing' | 'taskTrend' | 'highFrequencyEvents' | 'locationTop5' | 'partTop5';
  loading?: boolean;
}

const chartDate = ref<any[]>([]);
const props = withDefaults(defineProps<Props>(), {
  type: 'eventDistribution',
  loading: false,
});
const genderDistributionData = computed(() => {
  return chartDate.value.map((item, index) => ({
    name: item.certificateTypeName || '111',
    value: item.certificateTypeCount || '22',
    certificateTypeId: item.certificateTypeId || '33',
    percent: item.percentage || '50',
    color: genderColorConfig[index] || '',
  }));
});
const chartTitles = {
  eventDistribution: '异常事件分布',
  eventProcessing: '异常事件处置',
  taskTrend: '异常任务趋势',
  highFrequencyEvents: '巡检高频事件次数分布',
  locationTop5: '巡检高频事件位置TOP5',
  partTop5: '巡检高频事件部位TOP5',
};

const chartTitle = chartTitles[props.type];

defineOptions({ name: 'ChartCard' });
</script>

<style scoped lang="scss">
.chartCard {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;

  .left,
  .center,
  .right {
    display: flex;
    flex-direction: column;
    border: 1px solid #4a5c7e;
  }
}

.chartHeader {
  padding: 16px 20px;
  border-bottom: 1px solid #4a5c7e;
  text-align: center;
  align-items: center;
}
</style>
