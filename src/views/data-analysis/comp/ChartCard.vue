<template>
  <div class="chartCard">
    <div class="left flex-1 pt-[16px] pl-[20px] mr-[20px]">
      <ComHeaderC title="巡检高频事件次数分布" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <pieChart />
      </div>
    </div>
    <div class="center flex-1 pt-[16px] pl-[20px] mr-[20px]">
      <ComHeaderC title="巡检高频事件位置TOP5" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <BarChart :chartData="genderDistributionData" />
      </div>
    </div>
    <div class="right flex-1 pt-[16px] pl-[20px]">
      <ComHeaderC title="巡检高频事件点位TOP5" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <HbarChart :chartData="genderDistributionData" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue';
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import pieChart from '@/views/drawing/drawingLeft/pieChart.vue';
import BarChart from '../echarts/BarChart.vue';
import HbarChart from '../echarts/HbarChart.vue';
import { genderColorConfig } from './colorList';
import { topNEventGroupByPosition, topNEventGroupByVideoDevice } from '../fetchData';
import { InspectionVideoDataAnalysisXYChartData } from '../type';

interface Props {
  type?: 'eventDistribution' | 'eventProcessing' | 'taskTrend' | 'highFrequencyEvents' | 'locationTop5' | 'partTop5';
  loading?: boolean;
  orgCode?: string;
  startTime?: string | null;
  endTime?: string | null;
}

const chartDate = ref<InspectionVideoDataAnalysisXYChartData[]>([]);
const props = withDefaults(defineProps<Props>(), {
  type: 'eventDistribution',
  loading: false,
  orgCode: '',
  startTime: '',
  endTime: '',
});

const genderDistributionData = computed(() => {
  return chartDate.value.map((item, index) => ({
    name: item.xLabel || '未知',
    value: item.y1Value || '0',
    totalNum: item.totalNum || '0',
    y2Value: item.y2Value || '0',
    color: genderColorConfig[index] || '',
  }));
});

const fetchChartData = async () => {
  if (!props.orgCode) return;

  try {
    const query = {
      orgCode: props.orgCode,
      startTime: props.startTime,
      endTime: props.endTime,
    };

    let res;
    if (props.type === 'locationTop5') {
      res = await topNEventGroupByPosition(query);
    } else if (props.type === 'partTop5') {
      res = await topNEventGroupByVideoDevice(query);
    } else {
      return;
    }

    chartDate.value = res.data || [];
  } catch (error) {
    console.error('获取图表数据失败:', error);
    chartDate.value = [];
  }
};

onMounted(() => {
  fetchChartData();
});

watch(
  () => [props.orgCode, props.startTime, props.endTime],
  () => {
    fetchChartData();
  },
  { deep: true }
);

defineExpose({ fetchChartData });
defineOptions({ name: 'ChartCard' });
</script>

<style scoped lang="scss">
.chartCard {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;

  .left,
  .center,
  .right {
    display: flex;
    flex-direction: column;
    border: 1px solid #4a5c7e;
  }
}

.chartHeader {
  padding: 16px 20px;
  border-bottom: 1px solid #4a5c7e;
  text-align: center;
  align-items: center;
}
</style>
