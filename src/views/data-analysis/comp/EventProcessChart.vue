<template>
  <div class="event-process-chart">
    <div v-for="item in chartData" :key="item.name" class="event-item">
      <div class="processed-section">
        <div class="progress-bar" :style="{ width: `${getProgressWidth(item.processed)}%` }"></div>
        <div class="label">已处置 {{ item.processed }}</div>
      </div>

      <div class="event-name">{{ item.name }}</div>

      <div class="pending-section">
        <div class="label">待处置 {{ item.pending }}</div>
        <div class="progress-bar" :style="{ width: `${getProgressWidth(item.pending)}%` }"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';

defineOptions({ name: 'EventProcessChart' });

interface EventProcessData {
  name: string;
  processed: number;
  pending: number;
}

const props = defineProps<{
  chartData: EventProcessData[];
}>();

const maxValue = computed(() => {
  if (!props.chartData || props.chartData.length === 0) return 1;
  const allValues = props.chartData.flatMap((item) => [item.processed, item.pending]);
  return Math.max(...allValues, 1);
});

function getProgressWidth(value: number): number {
  if (value <= 0) return 0;
  const percentage = (value / maxValue.value) * 100;
  return Math.max(percentage, value > 0 ? 5 : 0);
}
</script>

<style scoped lang="scss">
.event-process-chart {
  width: 100%;
  height: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .event-item {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 12px;

    .processed-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;

      .progress-bar {
        height: 8px;
        background: linear-gradient(90deg, #00d087 0%, #03a11d 100%);
        border-radius: 4px;
        transition: width 0.6s ease-in-out;
        min-width: 2px;
      }

      .label {
        font-size: 12px;
        color: #00d087;
        background: rgba(0, 208, 135, 0.2);
        border: 1px solid #00d087;
        border-radius: 12px;
        padding: 2px 8px;
        white-space: nowrap;
      }
    }

    .event-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--skin-t1, #ffffff);
      text-align: center;
      min-width: 80px;
    }

    .pending-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;

      .label {
        font-size: 12px;
        color: #ff9c28;
        background: rgba(255, 156, 40, 0.2);
        border: 1px solid #ff9c28;
        border-radius: 12px;
        padding: 2px 8px;
        white-space: nowrap;
      }

      .progress-bar {
        height: 8px;
        background: linear-gradient(90deg, #ff9c28 0%, #e29700 100%);
        border-radius: 4px;
        transition: width 0.6s ease-in-out;
        min-width: 2px;
      }
    }
  }
}
</style>
