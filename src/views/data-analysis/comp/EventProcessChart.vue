<template>
  <div class="event-process-chart">
    <div class="chart-container">
      <div v-for="(item, index) in processedData" :key="item.name" class="event-item">
        <!-- 事件类型名称 -->
        <div class="event-name">{{ item.name }}</div>

        <!-- 数据标签区域 -->
        <div class="data-labels">
          <!-- 已处置标签 -->
          <div class="label processed">
            <span class="label-text">已处置</span>
            <span class="label-value">{{ item.processed }}</span>
          </div>

          <!-- 待处置标签 -->
          <div class="label pending">
            <span class="label-text">待处置</span>
            <span class="label-value">{{ item.pending }}</span>
          </div>
        </div>

        <!-- 进度条区域 -->
        <div class="progress-container">
          <!-- 已处置进度条 -->
          <div
            class="progress-bar processed-bar"
            :style="{
              width: `${getProgressWidth(item.processed, 'processed')}%`,
              backgroundColor: processedColor,
            }"
          ></div>

          <!-- 待处置进度条 -->
          <div
            class="progress-bar pending-bar"
            :style="{
              width: `${getProgressWidth(item.pending, 'pending')}%`,
              backgroundColor: pendingColor,
              marginTop: '4px',
            }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';

// 定义组件名称
defineOptions({ name: 'EventProcessChart' });

// 定义数据接口
interface EventProcessData {
  name: string; // 事件类型名称
  processed: number; // 已处置数量
  pending: number; // 待处置数量
}

// 定义组件属性
const props = defineProps<{
  chartData: EventProcessData[];
  processedColor?: string; // 已处置颜色
  pendingColor?: string; // 待处置颜色
  maxBarWidth?: number; // 最大进度条宽度百分比
}>();

// 默认颜色配置
const processedColor = computed(() => props.processedColor || '#00D087');
const pendingColor = computed(() => props.pendingColor || '#FF9C28');

// 处理后的数据
const processedData = computed(() => {
  if (!props.chartData || props.chartData.length === 0) {
    return [];
  }

  return props.chartData.map((item) => ({
    ...item,
    total: item.processed + item.pending,
  }));
});

// 计算最大值用于进度条比例
const maxValue = computed(() => {
  if (processedData.value.length === 0) return 1;

  const maxProcessed = Math.max(...processedData.value.map((item) => item.processed));
  const maxPending = Math.max(...processedData.value.map((item) => item.pending));

  return Math.max(maxProcessed, maxPending, 1); // 至少为1，避免除零
});

/**
 * 计算进度条宽度百分比
 * @param value 当前值
 * @param type 类型（processed 或 pending）
 * @returns 宽度百分比
 */
function getProgressWidth(value: number, type: 'processed' | 'pending'): number {
  if (value <= 0) return 0;

  const maxWidth = props.maxBarWidth || 100;
  const percentage = (value / maxValue.value) * maxWidth;

  // 确保最小宽度，让小数值也能显示
  return Math.max(percentage, value > 0 ? 5 : 0);
}

// 监听数据变化
watch(
  () => props.chartData,
  (newData) => {
    console.log('异常事件处置数据更新:', newData);
  },
  { deep: true, immediate: true }
);
</script>

<style scoped lang="scss">
.event-process-chart {
  width: 100%;
  height: 100%;
  padding: 16px;

  .chart-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;
  }

  .event-item {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .event-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--skin-t1, #ffffff);
      margin-bottom: 4px;
    }

    .data-labels {
      display: flex;
      gap: 12px;
      margin-bottom: 6px;

      .label {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;

        &.processed {
          background-color: rgba(0, 208, 135, 0.2);
          border: 1px solid #00d087;

          .label-text {
            color: #00d087;
          }

          .label-value {
            color: #00d087;
            font-weight: 600;
          }
        }

        &.pending {
          background-color: rgba(255, 156, 40, 0.2);
          border: 1px solid #ff9c28;

          .label-text {
            color: #ff9c28;
          }

          .label-value {
            color: #ff9c28;
            font-weight: 600;
          }
        }
      }
    }

    .progress-container {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .progress-bar {
        height: 8px;
        border-radius: 4px;
        transition: width 0.6s ease-in-out;
        min-width: 0;

        &.processed-bar {
          background: linear-gradient(90deg, #00d087 0%, #03a11d 100%);
          box-shadow: 0 2px 4px rgba(0, 208, 135, 0.3);
        }

        &.pending-bar {
          background: linear-gradient(90deg, #ff9c28 0%, #e29700 100%);
          box-shadow: 0 2px 4px rgba(255, 156, 40, 0.3);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .event-process-chart {
    padding: 12px;

    .chart-container {
      gap: 12px;
    }

    .event-item {
      gap: 6px;

      .event-name {
        font-size: 13px;
      }

      .data-labels {
        gap: 8px;

        .label {
          font-size: 11px;
          padding: 1px 6px;
        }
      }

      .progress-container {
        .progress-bar {
          height: 6px;
        }
      }
    }
  }
}
</style>
