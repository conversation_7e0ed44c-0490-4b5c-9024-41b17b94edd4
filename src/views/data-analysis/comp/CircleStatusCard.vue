<template>
  <div class="circleStatusCard">
    <div class="circleProgress" :style="{ borderColor: color }">
      <div class="progressValue">{{ value }}</div>
    </div>
    <div class="statusLabel">{{ label }}</div>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  value: string | number;
  label: string;
  color: string;
}

const props = defineProps<Props>();

defineOptions({ name: 'CircleStatusCard' });
</script>

<style scoped lang="scss">
.circleStatusCard {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.circleProgress {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
}

.progressValue {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.statusLabel {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}
</style>
