<template>
  <div class="chartCard">
    <div class="left flex-1 pt-[16px] pl-[20px] mr-[20px]">
      <ComHeaderC title="异常事件分布" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <pieCircleChart :chartData="genderDistributionData" />
      </div>
    </div>
    <div class="center flex-1 pt-[16px] pl-[20px] mr-[20px]">
      <ComHeaderC title="异常事件处置" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <EventProcessChart :chartData="eventProcessData" />
      </div>
    </div>
    <div class="right flex-1 pt-[16px] pl-[20px]">
      <ComHeaderC title="异常任务趋势" class="pageTitle" />
      <div class="flex-1 w-full h-full">
        <LineChart :chartData="genderDistributionData" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import pieCircleChart from '../echarts/pieCircleChart.vue';
import LineChart from '../echarts/LineChart.vue';
import EventProcessChart from './EventProcessChart.vue';
import { genderColorConfig } from './colorList';

interface Props {
  type?: 'eventDistribution' | 'eventProcessing' | 'taskTrend' | 'highFrequencyEvents' | 'locationTop5' | 'partTop5';
  loading?: boolean;
}

const chartDate = ref<any[]>([]);
const props = withDefaults(defineProps<Props>(), {
  type: 'eventDistribution',
  loading: false,
});
const genderDistributionData = computed(() => {
  return chartDate.value.map((item, index) => ({
    name: item.certificateTypeName,
    value: item.certificateTypeCount,
    certificateTypeId: item.certificateTypeId,
    percent: item.percentage,
    color: genderColorConfig[index],
  }));
});

// 异常事件处置数据
const eventProcessData = computed(() => {
  // 模拟数据，实际应该从 props 或 API 获取
  return [
    { name: '烟火识别', processed: 101, pending: 36 },
    { name: '违法停车', processed: 37, pending: 52 },
    { name: '人员离岗', processed: 18, pending: 73 },
    { name: '靠近水边', processed: 0, pending: 122 },
    { name: '水管泄漏', processed: 160, pending: 5 },
  ];
});

const chartTitles = {
  eventDistribution: '异常事件分布',
  eventProcessing: '异常事件处置',
  taskTrend: '异常任务趋势',
  highFrequencyEvents: '巡检高频事件次数分布',
  locationTop5: '巡检高频事件位置TOP5',
  partTop5: '巡检高频事件部位TOP5',
};

const chartTitle = chartTitles[props.type];

defineOptions({ name: 'ChartCard' });
</script>

<style scoped lang="scss">
.chartCard {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;

  .left,
  .center,
  .right {
    display: flex;
    flex-direction: column;
    border: 1px solid #4a5c7e;
  }
}

.chartHeader {
  padding: 16px 20px;
  border-bottom: 1px solid #4a5c7e;
  text-align: center;
  align-items: center;
}
</style>
