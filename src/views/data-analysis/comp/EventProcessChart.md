# EventProcessChart 异常事件处置组件

## 概述

`EventProcessChart` 是一个专门用于显示异常事件处置状态的可视化组件。该组件根据数值大小动态调整进度条长度，直观地展示各类异常事件的处置情况。

## 功能特性

### 1. 动态长度显示
- 根据数值大小自动调整进度条长度
- 支持已处置和待处置两种状态的同时显示
- 进度条长度按比例缩放，最大值自动计算

### 2. 数据标签
- 显示事件类型名称
- 已处置数量标签（绿色）
- 待处置数量标签（橙色）
- 数值清晰可读

### 3. 视觉效果
- 渐变色进度条
- 圆角设计
- 阴影效果
- 平滑动画过渡

### 4. 响应式设计
- 支持不同屏幕尺寸
- 移动端适配
- 自适应布局

## 使用方法

### 基本用法

```vue
<template>
  <div class="chart-container">
    <EventProcessChart :chartData="eventData" />
  </div>
</template>

<script setup>
import EventProcessChart from '@/views/data-analysis/comp/EventProcessChart.vue';

const eventData = [
  { name: '烟火识别', processed: 101, pending: 36 },
  { name: '违法停车', processed: 37, pending: 52 },
  { name: '人员离岗', processed: 18, pending: 73 },
  { name: '靠近水边', processed: 0, pending: 122 },
  { name: '水管泄漏', processed: 160, pending: 5 },
];
</script>
```

### 自定义颜色

```vue
<template>
  <EventProcessChart 
    :chartData="eventData"
    processedColor="#28CB43"
    pendingColor="#F7C52C"
  />
</template>
```

### 限制最大宽度

```vue
<template>
  <EventProcessChart 
    :chartData="eventData"
    :maxBarWidth="80"
  />
</template>
```

## API

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| chartData | EventProcessData[] | [] | 图表数据数组 |
| processedColor | string | '#00D087' | 已处置进度条颜色 |
| pendingColor | string | '#FF9C28' | 待处置进度条颜色 |
| maxBarWidth | number | 100 | 最大进度条宽度百分比 |

### 数据格式

```typescript
interface EventProcessData {
  name: string;        // 事件类型名称
  processed: number;   // 已处置数量
  pending: number;     // 待处置数量
}
```

### 数据示例

```javascript
const eventProcessData = [
  { name: '烟火识别', processed: 101, pending: 36 },
  { name: '违法停车', processed: 37, pending: 52 },
  { name: '人员离岗', processed: 18, pending: 73 },
  { name: '靠近水边', processed: 0, pending: 122 },
  { name: '水管泄漏', processed: 160, pending: 5 },
];
```

## 设计说明

### 长度计算逻辑
1. 自动计算所有数据中的最大值
2. 按比例缩放进度条长度
3. 确保小数值也有最小可见宽度（5%）
4. 支持自定义最大宽度限制

### 颜色方案
- **已处置**：绿色系渐变 (#00D087 → #03A11D)
- **待处置**：橙色系渐变 (#FF9C28 → #E29700)
- **标签背景**：半透明色彩，增强可读性

### 布局结构
```
事件项目
├── 事件名称
├── 数据标签区域
│   ├── 已处置标签
│   └── 待处置标签
└── 进度条区域
    ├── 已处置进度条
    └── 待处置进度条
```

## 技术实现

### 核心功能
1. **动态宽度计算**：根据数值比例计算进度条宽度
2. **响应式数据**：使用 Vue 3 Composition API
3. **CSS 动画**：平滑的宽度变化过渡效果
4. **类型安全**：完整的 TypeScript 类型定义

### 性能优化
- 使用 `computed` 计算属性缓存处理后的数据
- 避免不必要的重新渲染
- CSS 硬件加速的动画效果

## 样式定制

### CSS 变量
组件使用项目的 CSS 变量系统：
- `--skin-t1`：主要文本颜色
- `--skin-bg1`：背景颜色
- `--skin-bd1`：边框颜色

### 自定义样式
可以通过覆盖 CSS 类来自定义样式：

```scss
.event-process-chart {
  .event-item {
    .event-name {
      font-size: 16px;
      font-weight: 600;
    }
    
    .progress-bar {
      height: 10px;
      border-radius: 6px;
    }
  }
}
```

## 注意事项

1. **数据格式**：确保传入的数据符合 `EventProcessData` 接口
2. **数值范围**：支持任意正整数，0 值会显示为空进度条
3. **性能考虑**：大量数据时建议分页或虚拟滚动
4. **浏览器兼容性**：支持现代浏览器，IE11+ 需要 polyfill

## 示例

完整的使用示例请参考 `EventProcessChartExample.vue` 文件。
