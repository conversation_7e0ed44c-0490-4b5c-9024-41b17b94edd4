<template>
  <div class="topCards">
    <ComHeaderC title="智能巡检计划" class="pageTitle" />

    <div class="cardsContainer">
      <div class="leftCards">
        <div class="flex items-center gap-5 mr-1">
          <PlanCard title="全部计划" value="512" unit="个" icon="plan" />
          <PlanCard title="全部任务" value="512" unit="个" icon="task" />
        </div>
        <div class="w-full h-full">
          <pieCircleChart legend="rect" :chartData="[]" />
        </div>
      </div>
      <div class="centerChart">
        <div class="line mr-[20px]"></div>
        <PlanCard title="关联AI预测模块情况" value="128" unit="台" icon="ai" />
        <div class="line ml-[20px]"></div>
      </div>
      <div class="rightCards">
        <div class="flex items-center w-[184px] ml-[20px]">
          <PlanCard title="异常事件" value="38" unit="起" icon="event" class="" />
        </div>
        <div class="right-circle">
          <pieCircleChart
            :showLegend="false"
            :center="true"
            color="rgba(0, 185, 148, 1)"
            title="已处置"
            :chartData="[]"
          />
          <pieCircleChart
            :showLegend="false"
            :center="true"
            color="rgba(62, 133, 255, 1)"
            title="处置中"
            :chartData="[]"
          />
          <pieCircleChart
            :showLegend="false"
            :center="true"
            color="rgba(235, 149, 86, 1)"
            title="待处置"
            :chartData="[]"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import PlanCard from './PlanCard.vue';
import pieCircleChart from '../echarts/pieCircleChart2.vue';

defineOptions({ name: 'TopCards' });
</script>

<style scoped lang="scss">
.topCards {
  background: url('../assets/top_bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 16px 0 0 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pageTitle {
  // margin-bottom: 10px;
  width: 20% !important;
}

.cardsContainer {
  flex: 1;
  display: flex;
  // gap: 10px;
  height: 100%;
}

.leftCards {
  flex: 1;
  display: grid;
  grid-template-columns: 61% 39%;
}

.centerChart {
  width: 16rem;
  display: flex;
  justify-content: center;
  align-items: center;
  .line {
    width: 1px;
    height: 82px;
    background: linear-gradient(
      180deg,
      rgba(158, 192, 255, 0) 0%,
      rgba(158, 192, 255, 0.5) 54%,
      rgba(158, 192, 255, 0) 100%
    );
  }
}

.rightCards {
  flex: 1;
  display: flex;
  .right-circle {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }
}

.statusGroup {
  display: flex;
  gap: 16px;
  align-items: center;
}
</style>
