<template>
  <div class="topCards">
    <ComHeaderC title="智能巡检计划" class="pageTitle" />

    <div class="cardsContainer">
      <div class="leftCards">
        <div class="flex items-center gap-5 mr-1">
          <PlanCard title="全部计划" :value="overViewData.planCount" unit="个" icon="plan" />
          <PlanCard title="全部任务" :value="overViewData.taskCount" unit="个" icon="task" />
        </div>
        <div class="w-full h-full">
          <pieCircleChart legend="rect" :chartData="chartData" :key="chartData.join('-')" />
        </div>
      </div>
      <div class="centerChart">
        <div class="line mr-[20px]"></div>
        <PlanCard title="关联AI预测模块情况" :value="overViewData.deviceCount || 0" unit="台" icon="ai" />
        <div class="line ml-[20px]"></div>
      </div>
      <div class="rightCards">
        <div class="flex items-center w-[184px] ml-[20px]">
          <PlanCard title="异常事件" :value="overViewData.eventCount" unit="起" icon="event" class="" />
        </div>
        <div class="right-circle">
          <pieCircleChart
            :showLegend="false"
            :center="true"
            color="rgba(0, 185, 148, 1)"
            title="已处置"
            :value="overViewData.event4DoneCount"
            :key="chartData.join('-')"
          />
          <pieCircleChart
            :showLegend="false"
            :center="true"
            color="rgba(62, 133, 255, 1)"
            title="处置中"
            :value="overViewData.event4DoingCount"
            :key="chartData.join('-')"
          />
          <pieCircleChart
            :showLegend="false"
            :center="true"
            color="rgba(235, 149, 86, 1)"
            title="待处置"
            :value="overViewData.event4TodoCount"
            :key="chartData.join('-')"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import ComHeaderC from '@/components/header/ComHeaderC.vue';
import PlanCard from './PlanCard.vue';
import pieCircleChart from '../echarts/pieCircleChart2.vue';
import { overView } from '../fetchData';

defineOptions({ name: 'TopCards' });

interface Props {
  orgCode?: string;
}

const props = withDefaults(defineProps<Props>(), {
  orgCode: '',
});
const chartData = ref<any>([]);
const overViewData = ref<any>({});

const fetchOverViewData = async () => {
  if (!props.orgCode) return;

  try {
    const res = await overView({ orgCode: props.orgCode });
    overViewData.value = res.data;
    chartData.value = [
      res.data.task4DoingCount || 0, // 进行中
      res.data.task4DoneCount || 0, // 已完成
      res.data.task4TodoCount || 0, // 待开始
      res.data.task4NotDoCount || 0, // 未执行
    ];
  } catch (error) {
    console.error('获取概览数据失败:', error);
  }
};

onMounted(() => {
  fetchOverViewData();
});

// 监听orgCode变化，重新获取数据
watch(
  () => props.orgCode,
  () => {
    fetchOverViewData();
  },
  { immediate: true }
);

defineExpose({ fetchOverViewData });
</script>

<style scoped lang="scss">
.topCards {
  background: url('../assets/top_bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 16px 0 0 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pageTitle {
  // margin-bottom: 10px;
  width: 20% !important;
}

.cardsContainer {
  flex: 1;
  display: flex;
  height: 100%;
  min-width: 0; // 确保flex子项可以收缩
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); // 与组织树动画同步
}

.leftCards {
  flex: 1;
  min-width: 0; // 允许收缩
  display: grid;
  grid-template-columns: minmax(200px, 61%) minmax(120px, 39%);
  gap: 8px;
}

.centerChart {
  flex-shrink: 0; // 防止被过度压缩
  min-width: 200px; // 设置最小宽度而不是固定宽度
  max-width: 16rem; // 最大宽度保持原来的值
  width: clamp(200px, 20vw, 16rem); // 响应式宽度
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .line {
    width: 1px;
    height: 82px;
    background: linear-gradient(
      180deg,
      rgba(158, 192, 255, 0) 0%,
      rgba(158, 192, 255, 0.5) 54%,
      rgba(158, 192, 255, 0) 100%
    );
  }
}

.rightCards {
  flex: 1;
  min-width: 0; // 允许收缩
  display: flex;
  .right-circle {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(3, minmax(60px, 1fr)); // 确保每个圆形图表有最小宽度
    gap: 4px; // 添加间距
    min-width: 0; // 确保grid可以收缩
  }
}

.statusGroup {
  display: flex;
  gap: 16px;
  align-items: center;
}
</style>
