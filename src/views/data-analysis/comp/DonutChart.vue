<template>
  <div class="donutChart">
    <div class="chartContainer">
      <!-- ECharts 图表容器，暂时显示占位符 -->
      <div class="chartPlaceholder">
        <div class="placeholderText">环形图表</div>
        <div class="placeholderSubtext">ECharts 图表位置</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'DonutChart' });
</script>

<style scoped lang="scss">
.donutChart {
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.chartContainer {
  width: 120px;
  height: 80px;
  position: relative;
}

.chartPlaceholder {
  width: 100%;
  height: 100%;
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
}

.placeholderText {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}

.placeholderSubtext {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.4);
  text-align: center;
  margin-top: 2px;
}
</style>
