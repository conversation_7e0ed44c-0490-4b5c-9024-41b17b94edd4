<template>
  <div class="statCard">
    <div class="cardContent">
      <div class="w-full h-full">
        <div v-if="value" class="valueSection">
          <div class="value">{{ value }}</div>
          <div v-if="unit" class="unit">{{ unit }}</div>
        </div>
        <div class="title">{{ title }}</div>
      </div>
      <div class="w-full h-full">
        <pieCircleChart :chartData="chartData" :showLegend="false" center="50%" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import pieCircleChart from '../echarts/pieCircleChart.vue';
interface Props {
  title?: string;
  value?: number | string;
  unit?: string;
  icon?: string;
  color?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  value: '',
  unit: '',
  icon: '',
  color: '#1890ff',
});

const chartData = ref<any[]>([]);

defineOptions({ name: 'StatCard' });
</script>

<style scoped lang="scss">
.statCard {
  flex: 1;
  border-radius: 8px;
  border: 1px solid #4a5c7e;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.1);
  }
}

.cardContent {
  padding: 20px;
  height: 120px;
  display: grid;
  grid-template-columns: auto minmax(0, 1fr);
  min-width: 0;
  gap: 10px;
}

.valueSection {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.value {
  font-size: 32px;
  font-weight: 600;
  color: var(--skin-t1);
}

.unit {
  font-size: 16px;
  color: var(--skin-t6);
}

.title {
  font-size: 14px;
  color: var(--skin-t6);
  text-align: center;
}
</style>
