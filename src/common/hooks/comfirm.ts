import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { $toast } from '@/common/shareContext/useToastCtx';

/**
 * 操作二次确认
 * @param msg 确认提示信息
 */
interface IComfirm {
  msg?: string | undefined; //提示信息
  title?: string | undefined; //标题
  posT?: string | undefined; //确定按钮文本
  negaT?: string | undefined; //取消按钮文本
  cannelText?: string; //取消提示
  submitText?: string; //确定提示
}
export function useActionComfirm(data: IComfirm) {
  return new Promise((resolve, reject) => {
    $dialog.success({
      title: data.title || '提示',
      content: data.msg || '确认此操作码？',
      positiveText: data.posT || '确定',
      negativeText: data.negaT || '取消',
      onPositiveClick: () => {
        resolve(true);
      },
      onNegativeClick: () => {
        $toast.info(data.cannelText || '取消操作');
        reject(false);
      },
    });
  });
}
export function useActionWarning(data: IComfirm) {
  console.log('data', data);
  return new Promise((resolve, reject) => {
    $dialog.warning({
      title: data.title || '提示',
      content: data.msg || '确认此操作码？',
      positiveText: data.posT || '确定',
      negativeText: data.negaT,
      onPositiveClick: () => {
        resolve(true);
      },
      onNegativeClick: () => {
        $toast.info(data.cannelText || '取消操作');
        reject(false);
      },
    });
  });
}
