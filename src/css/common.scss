/**
 * 公共类
 */

:root {
  --header-height: 64px;

  --com-border-radius: 4px;
  --com-border: unset;
  --com-box-shadow: unset;
  --com-box-bg: var(--skin-bg1-rgb);
  --com-container-bg: var(--skin-bg1-rgb);
  --com-container-shadow: unset;
  --com-primary-color: var(--skin-c1);
}

body {
  font-size: 14px;
}

.light {
  background: var(--skin-bg2);

  /* 容器背景 */
  .com-container-bg {
    background: rgba(238, 247, 255, 0.62);
  }
}

.dark {
  background: url('../assets/bg.png') center 0 no-repeat var(--skin-bg2);
  background-size: cover;

  /* 容器背景 */
  .com-container-bg {
    background: rgba(var(--com-container-bg), 0);
  }
}

.com-header {
  background: var(--com-primary-color);
  color: var(--skin-t1);
  width: 100%;
  height: var(--header-height);
  padding: 0 24px;
}

.com-container-border {
  border: var(--com-border);
  border-radius: var(--com-border-radius);
  box-shadow: var(--com-container-shadow);
}

/* 通用容器 */
.com-container {
  @extend .com-container-bg;
  @extend .com-container-border;
}

/* 基础容器尺寸、颜色 private */
._container-base {
  position: relative;
  height: calc(100vh - var(--header-height) - 24px - 24px);
  margin: 24px 24px 0;
  color: #000;
  overflow: hidden;
}

/* 表格容器外层 */
.com-table-wrap {
  @extend ._container-base;
  display: grid;
  grid-template-rows: auto 1fr;
}

/* 表格容器 */
.com-table-container {
  @extend .com-container-bg;
  @extend .com-container-border;
  padding: 24px 24px 16px;
}

.com-table-filter {
  @extend .com-container-bg;
  @extend .com-container-border;
  padding: 24px;
}

.com-table-filter-nb {
  @extend .com-container-bg;
  @extend .com-container-border;
  border-radius: unset;
  padding: 24px;
}

// 只有底部带圆角 borer-left-right,
.com-table-filter-blr {
  @extend .com-container-bg;
  @extend .com-container-border;
  border-top-left-radius: unset;
  border-top-right-radius: unset;
  padding: 24px;
}

/* Box盒子（比container小的模块） */

.com-box-bg {
  background: rgba(var(--com-box-bg), 0.2);
}

.com-box-border {
  border: var(--com-border);
  border-radius: var(--com-border-radius);
  box-shadow: var(--com-box-shadow);
}

.com-box {
  @extend .com-box-bg;
  @extend .com-box-border;
}

.com-action-button {
  --n-text-color: var(--com-primary-color) !important;
}

/* com-g-x-x -> */

.com-g-row-aa {
  display: grid;
  grid-template-rows: auto auto;
  grid-template-columns: minmax(0, 1fr);
}

.com-g-row-a1 {
  display: grid;
  grid-template-rows: auto minmax(0, 1fr);
  grid-template-columns: minmax(0, 1fr);
  min-height: 0;
}

.com-g-row-1a {
  display: grid;
  grid-template-rows: minmax(0, 1fr) auto;
  grid-template-columns: minmax(0, 1fr);
  min-height: 0;
}

.com-g-row-aa1 {
  display: grid;
  grid-template-rows: auto auto minmax(0, 1fr);
  grid-template-columns: minmax(0, 1fr);
  min-height: 0;
}

.com-g-row-full {
  grid-template-rows: 100%;
}

.com-g-col-aa {
  display: grid;
  grid-template-columns: auto auto;
}

.com-g-col-a1 {
  display: grid;
  grid-template-columns: auto minmax(0, 1fr);
  min-width: 0;
}

.com-g-col-1a {
  display: grid;
  grid-template-columns: minmax(0, 1fr) auto;
  min-width: 0;
}

.com-g-col-full {
  grid-template-columns: 100%;
}

/* com-g-x-x  <- */

.com-empty {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  justify-items: center;
}

.com-batch-remove-button {
  position: absolute !important;
  bottom: 16px;
  left: 24px;
}

@mixin autofill($color, $theme) {
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover,
  textarea:-webkit-autofill:focus,
  select:-webkit-autofill,
  select:-webkit-autofill:hover,
  select:-webkit-autofill:focus {
    -webkit-background-clip: text;
    -webkit-text-fill-color: $color;
    color-scheme: $theme;
  }
}

.com-autofill-none-dark {
  @include autofill(rgba(255, 255, 255, 0.9), dark);
}

.com-autofill-none-light {
  @include autofill(rgba(0, 0, 0, 0.9), light);
}

.light {
  .com-autofill-none {
    @extend .com-autofill-none-dark;
  }
}

.dark {
  .com-autofill-none {
    @extend .com-autofill-none-light;
  }
}
