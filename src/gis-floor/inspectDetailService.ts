import { FloorGisService } from './floorGisService';
import { IDeviceRow } from './type';
import { ref } from 'vue';
import { IObj } from '@/types';

export class InspectDetailService {
  // 巡检点位详情框
  static showInspectDetail = ref(false);
  static curInspectInfo = ref<IObj<any>>({});

  static onDeviceClick(data: IDeviceRow, e: any, obj: any, target: any) {
    const deviceTotal = FloorGisService.inspectList.value.length;
    InspectDetailService.curInspectInfo.value = {
      deviceTotal,
      positionNo: data.positionNo,
    };

    InspectDetailService.showInspectDetail.value = true;
  }
}
