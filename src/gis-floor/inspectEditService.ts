import { FloorGisService } from './floorGisService';
import { IDeviceRow } from './type';

export class InspectEditService {
  static onDeviceClick(data: IDeviceRow, e: any, obj: any, target: any) {
    const _list = FloorGisService.inspectList.value;
    const filterList = _list.filter((item: IDeviceRow) => item.deviceId === data.deviceId);
    if (filterList[0]) {
      const _index = _list.findIndex((item: IDeviceRow) => item.deviceId === data.deviceId);
      if (_index > -1) {
        _list.splice(_index, 1);
      }
    } else {
      _list.push(data);
    }
    FloorGisService.showInspectList(_list);
  }
}
