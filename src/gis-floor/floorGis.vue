<template>
  <n-spin :show="!GisService.isReady" class="w-full h-full" content-class="w-full h-full">
    <div class="map-wrapper">
      <div id="floorGis" class="map"></div>
      <div v-show="GisService.floorLoadFail.value" class="no-gis">
        <img class="empty-img" src="./assets/empty.png" alt="" srcset="" />
        <span class="empty-text">暂无楼层图</span>
      </div>
      <!-- floor control -->
      <FloorControl v-if="showDeviceLoc" class="floor-control" ref="FloorRef" />
      <!-- device prev/next -->
      <template v-if="showDeviceCarousel">
        <DeviceCarousel />
      </template>

      <CollapseSwitch v-if="showCollapse" />

      <!-- 巡检点位详情 -->
      <InspectInfoDia v-model:show="InspectDetailService.showInspectDetail.value" :data="curInspectInfo" />
    </div>
  </n-spin>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, watchEffect, WatchHandle } from 'vue';
import FloorControl from './floorControl.vue';
import DeviceCarousel from './deviceCarousel.vue';
import CollapseSwitch from './collapseSwitch.vue';
import { FloorGisService as GisService } from './floorGisService';
import { getDevicesList } from './featch';
import { IDeviceRow } from './type';
import InspectInfoDia from '@/views/inspect-com/InspectInfoDia.vue';
import { InspectDetailService } from './inspectDetailService';

import { EGisType, TGisType } from './constant';

defineOptions({ name: 'FloorGisComp' });

interface IProps {
  type: TGisType;
  buildId?: string | null;
  floorId?: string | null;
  defaultInspectList?: Array<IDeviceRow>;
  deviceList?: Array<IDeviceRow>;
}
const props = withDefaults(defineProps<IProps>(), {
  type: EGisType.INSPECTEDIT,
  buildId: '',
  floorId: '',
  defaultInspectList: () => [],
  deviceList: () => [],
});

const showDeviceLoc = ref(true);
const FloorRef = ref();

const showDeviceCarousel = ref(false);

const showCollapse = ref(false);

const unWatcher: WatchHandle[] = [];

const curInspectInfo = computed(() => InspectDetailService.curInspectInfo.value);

const emits = defineEmits(['on-update:floor', 'on-update:inspect']);

// 请求poi数据
const showFloorPoiList = async () => {
  try {
    const isMultiple = GisService.curUnitInfo.value?.floorMapType === IndoorMap.ViewType.IndoorAreaVector3DM;
    const unitInfo = GisService.curUnitInfo.value;
    const floorId = isMultiple ? '' : GisService.curFloorId.value;

    if (GisService.needLoadPOI) {
      const res = await getDevicesList({
        deptId: unitInfo?.treeId,
        buildingId: props.buildId,
        floorId,
      });
      GisService.poiList = res.data.map((item) => ({
        ...item,
        buildId: item.buildingId,
      }));
      // 多楼层模式，使用缓存数据
      if (isMultiple) GisService.needLoadPOI = false;
    }

    GisService.showPoiList(GisService.poiList);
  } catch (error) {}
};

// 绘制巡检路线
const showInspectList = () => {
  if (GisService.inspectList.value.length < 1) return;
  GisService.showInspectList(GisService.inspectList.value);
};

// 编辑巡检点位路径逻辑
const initInspectEdit = () => {
  // 根据props赋值
  const initDefaultValue = () => {
    GisService.curBuildId.value = props.buildId as string;
    GisService.needLoadPOI = true;

    if (props.defaultInspectList?.length > 0) {
      GisService.inspectList.value = props.defaultInspectList.map((item) => ({
        ...item,
        buildId: item.buildingId,
      }));
      GisService.curFloorId.value = GisService.inspectList.value[0].floorId;
    } else {
      GisService.inspectList.value = [];
      GisService.curFloorId.value = '';
    }

    const isMultiple = GisService.curUnitInfo.value?.floorMapType === IndoorMap.ViewType.IndoorAreaVector3DM;
    if (isMultiple) {
      showCollapse.value = true;
    }
  };

  // 监听楼栋变化
  const unBuildWatcher = watch(
    () => props.buildId,
    async (newV, oldV) => {
      if (!newV || newV === oldV) return;
      initDefaultValue();
      FloorRef.value?.getFloorListData();
    },
    {
      immediate: true,
    }
  );

  // 监听楼层变化
  const unFloorWatcher = watch(
    () => GisService.curFloorId.value,
    async (newV, oldV) => {
      if (!newV || newV === oldV) return;
      await GisService.changeFloor();
      // 楼层所有设备
      await showFloorPoiList();
      // 每次路径
      showInspectList();
    },
    {
      immediate: true,
    }
  );

  // 巡检路径
  const unInspectWatcher = watch(
    () => GisService.inspectList.value,
    (nv) => {
      emits('on-update:inspect', nv);
    },
    {
      deep: true,
    }
  );

  unWatcher.push(unBuildWatcher, unFloorWatcher, unInspectWatcher);
};

// 回显巡检点位路径逻辑
const initInspectDetail = () => {
  // 根据props.defaultInspectList赋值
  function initDefaultValue() {
    GisService.inspectList.value = props.defaultInspectList.map((item) => ({
      ...item,
      buildId: item.buildingId,
    }));
    GisService.curBuildId.value = GisService.inspectList.value[0].buildId;
    GisService.curFloorId.value = GisService.inspectList.value[0].floorId;

    GisService.needLoadPOI = false;

    const isMultiple = GisService.curUnitInfo.value?.floorMapType === IndoorMap.ViewType.IndoorAreaVector3DM;
    if (isMultiple) {
      showCollapse.value = true;
    } else {
      showDeviceCarousel.value = true;
    }
  }

  initDefaultValue();

  // 监听楼栋变化
  const unBuildWatcher = watch(
    () => GisService.curBuildId.value,
    (newV) => {
      if (!newV) return;

      FloorRef.value?.getFloorListData();
    },
    {
      immediate: true,
    }
  );

  // 监听楼层变化
  const unFloorWatcher = watch(
    () => GisService.curFloorId.value,
    async (newV) => {
      if (!newV) return;
      await GisService.changeFloor();
      // 根据现有路径点位撒点后才能绘制路线
      const isMultiple = GisService.curUnitInfo.value?.floorMapType === IndoorMap.ViewType.IndoorAreaVector3DM;
      const _list = isMultiple
        ? GisService.inspectList.value
        : GisService.inspectList.value.filter((item) => item.floorId === newV);
      if (_list.length > 0) {
        GisService.showPoiList(_list);
      }
      GisService.showInspectList(GisService.inspectList.value);
    },
    {
      immediate: true,
    }
  );

  unWatcher.push(unBuildWatcher, unFloorWatcher);
};

// 点位
const initDeviceLoc = async () => {
  showDeviceLoc.value = false;

  // 根据props.deviceList赋值
  const list = props.deviceList.map((item) => ({
    ...item,
    buildId: item.buildingId,
  }));
  GisService.curBuildId.value = list[0].buildId;
  GisService.curFloorId.value = list[0].floorId;

  // 加载楼层和点位
  await GisService.renderFloorGis();
  GisService.showPoiList(list);
};

const initPage = () => {
  GisService.gisType = props.type;

  if (GisService.gisType === EGisType.INSPECTEDIT) {
    initInspectEdit();
  }

  if (GisService.gisType === EGisType.INSPECTDETAIL) {
    initInspectDetail();
  }

  if (GisService.gisType === EGisType.DEVICELOC) {
    initDeviceLoc();
  }
};

onMounted(async () => {
  try {
    await GisService.init();

    initPage();
  } catch (e) {
    console.error('GIS PAGE:', e);
  }
});

onBeforeUnmount(() => {
  unWatcher.forEach((item) => item());

  GisService.destoryGis();
});
</script>

<style scoped lang="scss">
.map-wrapper {
  @apply relative w-full h-full overflow-hidden;
  background-color: var(--skin-bg0);
  border: 1px solid;
  border-color: var(--skin-bd2);

  .map {
    @apply absolute top-0 left-0 w-full h-full;
    z-index: 0;
  }
  .no-gis {
    @apply absolute top-0 left-0 w-full h-full flex flex-col items-center justify-center gap-[10px] bg-[#112232];
    z-index: 1;
    font-size: 16px;
    color: #8897b8;
  }

  .floor-control {
    @apply absolute top-[20px] right-[10px];
    z-index: 2;
  }
}
</style>
