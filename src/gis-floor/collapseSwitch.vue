<template>
  <div class="collapse-switch">
    <span>{{ label }}</span>
    <n-switch v-model:value="active" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { FloorGisService as GisService } from './floorGisService';

defineOptions({ name: 'CollapseSwitch' });

const active = ref(true);
const label = computed(() => (active.value ? '楼层展开' : '楼层折叠'));

const emits = defineEmits(['change']);

watch(
  () => active.value,
  () => {
    GisService.CollapseChange(active.value);
  }
);
</script>

<style scoped lang="scss">
.collapse-switch {
  @apply flex items-center gap-[10px];
  position: absolute;
  top: 20px;
  left: 10px;
}
</style>
