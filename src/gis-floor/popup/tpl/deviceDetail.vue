<template>
  <div :class="$style.popupWrap">
    <div :class="$style.header">
      <span :class="$style.title">{{ deviceInfo.deviceName || '--' }}</span>
      <div :class="$style.location">{{ deviceInfo.location || '--' }}</div>
    </div>
    <div :class="$style.content">
      <div :class="$style.infoItem">
        <span :class="$style.label">设备编号：</span>
        <span :class="$style.value">{{ deviceInfo.deviceCode || '--' }}</span>
      </div>
      <div :class="$style.infoItem">
        <span :class="$style.label">主机回路点位：</span>
        <span :class="$style.value">{{ deviceInfo.hostPoint || '--' }}</span>
      </div>
      <div :class="$style.infoItem">
        <span :class="$style.label">品牌型号：</span>
        <span :class="$style.value">{{ deviceInfo.brandModel || '--' }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import _isFunction from 'lodash-es/isFunction';
import { getDeviceInfo } from '@/views/inspect-plan/fetchData';

defineOptions({ name: 'gisPopupTplDeviceDetail' });

const props = defineProps({
  tplData: {
    type: Object,
    default: () => {},
  },
  handleClose: {
    type: Function,
    default: () => void 1,
  },
});

// 设备信息
const deviceInfo = ref({
  deviceName: '',
  location: '',
  deviceCode: '',
  hostPoint: '',
  brandModel: '',
});

// 获取设备信息
async function fetchDeviceInfo() {
  const deviceId = props.tplData?.deviceId;
  if (deviceId) {
    const res: any = await getDeviceInfo(deviceId);
    if (res.code === 'success') {
      const data = res.data[0];
      deviceInfo.value = {
        deviceName: data.deviceName,
        location: data.buildingName + data.floorName + data.deviceAddress,
        deviceCode: deviceId,
        hostPoint: data.laMake + '-' + data.laLoop + '-' + data.laPoint,
        brandModel: JSON.parse(data.produceInfo).brand + '-' + data.model,
      };
    }
  }
}

onMounted(() => {
  fetchDeviceInfo();
});

// 关闭弹窗
function close() {
  if (_isFunction(props.handleClose)) {
    props.handleClose();
  }
}
</script>

<style module lang="scss">
.popupWrap {
  position: relative;
  width: 357px;
  height: 220px;
  background: url('@/assets/3dpop_bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 13px 39px;
  color: #ffffff;
  display: grid;
  grid-template-rows: 40% 60%;
  gap: 8px;

  .header {
    .title {
      font-family:
        Alibaba PuHuiTi 2,
        Alibaba PuHuiTi 20;
      font-weight: 600;
      font-size: 18px;
    }
    .location {
      font-weight: 400;
      font-size: 14px;
    }
  }

  .content {
    .infoItem {
      display: flex;
      margin-bottom: 5px;
      font-weight: 400;
      font-size: 14px;

      .value {
        text-align: left;
        flex: 1;
        word-break: break-all;
      }
    }
  }
}
</style>
