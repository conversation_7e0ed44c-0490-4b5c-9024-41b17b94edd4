<template>
  <div :class="$style.popupWrap">
    <div :class="$style.content">
      <span :class="[$style['result-tag'], $style['result-tag_' + tplData.videoResult]]">
        {{ tplData.videoResultName }}
      </span>
      <div>
        <span>巡检时间：</span>
        <span>{{ tplData.videoTime || '--' }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import _isFunction from 'lodash-es/isFunction';

defineOptions({ name: 'inspectTime' });

const props = defineProps({
  tplData: {
    type: Object,
    default: () => {},
  },
  handleClose: {
    type: Function,
    default: () => void 1,
  },
});

// 关闭弹窗
function close() {
  if (_isFunction(props.handleClose)) {
    props.handleClose();
  }
}

const showVideoDia = ref(false);
const videoOpen = () => {
  showVideoDia.value = true;
};
</script>

<style module lang="scss">
.popupWrap {
  background: #10315b;
  box-shadow: inset 0px 3px 7px 0px rgba(16, 176, 243, 0.76);
  border-radius: 6px;
  border: 1px solid #0165b9;
  transform: skewX(-15deg);

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    width: auto;
    padding: 8px 10px;
    font-size: 14px;
    font-weight: bold;
    color: var(--com-color-label);
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1;
    transform: skewX(15deg);

    .result-tag {
      font-size: 14px;
      color: #fff;

      &.result-tag_0 {
        color: #15753b;
      }
      &.result-tag_2 {
        color: #f32e2e;
      }
    }
  }
}
</style>
