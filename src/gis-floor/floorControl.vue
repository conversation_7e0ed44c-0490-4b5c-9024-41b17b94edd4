<template>
  <div class="control-wrapper">
    <!-- <BsChevronDoubleUp class="arrow" /> -->
    <n-scrollbar content-class="h-full" :style="{ maxHeight: '350px' }">
      <ul class="floor-list">
        <li
          v-for="item in floorList"
          :key="item.floorId"
          :class="['floor-item', curFloorId === item.floorId ? 'floor-item_actived' : '']"
          @click="changeHandle(item.floorId)"
        >
          {{ item.floorName }}
        </li>
      </ul>
    </n-scrollbar>
    <!-- <BsChevronDoubleDown class="arrow" /> -->
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { FloorGisService as GisService } from './floorGisService';
import { getFloorList } from '@/views/inspect-plan/fetchData';

defineOptions({ name: 'floorControlComp' });

const curFloorId = GisService.curFloorId;

const floorList = ref<
  Array<{
    floorId: string;
    floorName: string;
    [key: string]: any;
  }>
>([]);

const getFloorListData = () => {
  const unitId = GisService.curUnitInfo.value?.unitId || '';
  getFloorList({
    unitId,
    buildId: GisService.curBuildId.value,
  }).then((res: any) => {
    if (res.code === 'success') {
      const _data = res.data || [];
      floorList.value = _data;
      if (!GisService.curFloorId.value) {
        GisService.curFloorId.value = _data[0]?.floorId;
        // changeHandle(_data[0]?.floorId);
      }
    }
  });
};

const emits = defineEmits(['change']);
const changeHandle = (floorId: string) => {
  GisService.curFloorId.value = floorId;
  // emits('change', floorId);
};

defineExpose({
  getFloorListData,
});
</script>

<style scoped lang="scss">
.control-wrapper {
  @apply py-[10px] flex flex-col gap-[10px] items-center;
}

.arrow {
  transform: translateX(-5px);
  font-size: 18px;
}

.floor-list {
  @apply pr-[10px] flex flex-col gap-[10px];

  .floor-item {
    @apply p-[8px] cursor-pointer;
    background-color: var(--skin-bg6);
    color: var(--skin-t);
    border: 1px solid;
    border-color: var(--skin-bd4);
    border-radius: 4px;

    &:hover {
      border-color: var(--skin-bd5);
    }
    &.floor-item_actived {
      background-color: var(--skin-bg7);
      border-color: var(--skin-bd5);
    }
  }
}
</style>
