<template>
  <div class="carousel-control" v-show="curFloorPioList.length > 0">
    <div :class="['carousel-btn prev-btn', prevFloorId ? '' : 'btn-disable']" @click="prevHandle">上一层</div>
    <div :class="['carousel-btn next-btn', nextFloorId ? '' : 'btn-disable']" @click="nextHandle">下一层</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { FloorGisService as GisService } from './floorGisService';

defineOptions({ name: 'deviceCarouselComp' });

const curFloorPioList = computed(() => {
  const _list = GisService.inspectList.value.filter((item) => item.floorId === GisService.curFloorId.value);
  return _list;
});

const prevFloorId = computed(() => {
  let id = '';
  const _list = curFloorPioList.value;
  if (_list.length > 0) {
    const firstOne = _list[0];
    const firstIndex_InspectList = GisService.inspectList.value.findIndex(
      (item) => item.deviceId === firstOne.deviceId
    );
    if (firstIndex_InspectList > 0) {
      id = GisService.inspectList.value[firstIndex_InspectList - 1].floorId;
    }
  }

  return id;
});
const nextFloorId = computed(() => {
  let id = '';
  const _list = curFloorPioList.value;
  if (_list.length > 0) {
    const lastOne = _list[_list.length - 1];
    const lastIndex_Inspect = GisService.inspectList.value.findIndex((item) => item.deviceId === lastOne.deviceId);
    const length = GisService.inspectList.value.length;
    if (lastIndex_Inspect < length - 1) {
      id = GisService.inspectList.value[lastIndex_Inspect + 1].floorId;
    }
  }

  return id;
});

const prevHandle = () => {
  if (!prevFloorId.value) return;
  GisService.curFloorId.value = prevFloorId.value;
};
const nextHandle = () => {
  if (!nextFloorId.value) return;
  GisService.curFloorId.value = nextFloorId.value;
};
</script>

<style scoped lang="scss">
.carousel-control {
  @apply absolute top-[50%] left-[10px];
  transform: translateY(-50%);
  z-index: 2;

  .carousel-btn {
    @apply px-[8px] py-[4px] text-[14px] cursor-pointer;
    // background-color: var(--skin-bg7);
    background-color: #1c6fff;
    color: var(--skin-t);
    border: 1px solid #fff;
    border-radius: 4px;
    &.prev-btn {
      @apply left-[30px] mb-[8px];
    }
    &.next-btn {
      @apply right-[30px];
    }

    &.btn-disable {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}
</style>
