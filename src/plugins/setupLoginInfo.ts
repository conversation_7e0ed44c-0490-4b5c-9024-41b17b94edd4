import { useStore } from '@/store';
import { getUrlParams } from '@/utils/getParams';
import { api } from '@/api';
import { IObj } from '@/types';
import { $http } from '@tanzerfe/http';

function getTokenLoginInfoByTokenApi(query: IObj<any>) {
  const url = api.getUrl(api.type.login, api.name.serve.getTokenLoginInfoByToken, query);
  console.log(url, '=======================');
  return $http.get<any>(url, { data: { _cfg: { showTip: true, showOkTip: false } } });
}
function loginFn() {
  return new Promise(async (resolve, reject) => {
    const ui = useStore();
    const quer = await getUrlParams(location.href);
    console.log(quer, '获取头部登录参数');
    if (!quer || !quer.sysCode || !quer.token) {
      console.log('参数不全----');
      reject(false);
    }
    const curSysCode = quer.sysCode.split('#/');
    console.log(curSysCode, '分离出的syscode');
    //已参数换token
    getTokenLoginInfoByTokenApi({ sysCode: curSysCode[0], token: quer.token })
      .then(async (info) => {
        console.log('登录接口成功');
        await ui.setUser(info.data);
        resolve(ui);
      })
      .catch(() => {
        console.log('登录接口异常了');
        reject();
      });
  });
}
// 定义一个函数来设置favicon
function setFavicon() {
  const store = useStore();
  const faviconLink = document.querySelector("link[rel*='icon']") || document.createElement('link');
  (faviconLink as any).type = 'image/x-icon';
  (faviconLink as any).rel = 'shortcut icon';

  (faviconLink as any).href = store.userInfo.logoPicUrl;
  document.getElementsByTagName('head')[0].appendChild(faviconLink);
  //document.title = title;
  document.title = '智能巡检系统';
}

export function setupLoginInfo() {
  const ui = useStore();

  // 统一的登录处理函数
  function handleLogin() {
    const params = getUrlParams(location.href);

    // 如果URL中有id参数，跳过登录
    if (params.id) {
      console.log('检测到id参数，跳过登录');
      return Promise.resolve();
    }

    // 如果URL中有token和sysCode，强制重新登录（无论是否已登录）
    if (params.token && params.sysCode) {
      console.log('检测到登录参数，开始登录');
      // 清除旧的用户信息，强制重新登录
      ui.$reset();
      return loginFn().then((res) => {
        console.log('登录成功', res);
        setFavicon();
      });
    }

    // 如果已有用户信息且没有token参数，跳过登录
    if (ui.userInfo.id) {
      setFavicon();
      return Promise.resolve();
    }

    // 默认登录流程
    return loginFn().then((res) => {
      console.log('登录成功', res);
      setFavicon();
    });
  }

  // 页面加载时检查
  return handleLogin();
}
