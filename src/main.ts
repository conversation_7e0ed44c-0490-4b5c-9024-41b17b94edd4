/*
 * @Author: <PERSON>yaling <EMAIL>
 * @Date: 2025-05-14 16:20:33
 * @LastEditors: Zhangyaling <EMAIL>
 * @LastEditTime: 2025-05-14 17:09:26
 * @FilePath: \ehs-inspect\src\main.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createApp } from 'vue';
import App from './App.vue';
import { setupApi, setupAssets, setupRouter, setupStore, setupLoginInfo } from '@/plugins';
import { BridgeService } from './service/bridge/BridgeService.ts';
import { BridgeRemoteService } from '@/service/bridge/BridgeRemoteService.ts';
import '@tanzerfe/ifm-child';
async function setupApp() {
  const app = createApp(App);

  // import assets: js、css
  setupAssets();

  // register store
  setupStore(app);

  // api
  setupApi();

  await setupLoginInfo();

  // register module router
  await setupRouter(app);

  // 注册代理服务(开发)
  import.meta.env.DEV && BridgeService.registerProxy();
  BridgeRemoteService.init();

  app.mount('#app');
}

setupApp();
