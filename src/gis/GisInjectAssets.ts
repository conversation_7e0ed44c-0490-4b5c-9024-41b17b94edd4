import { ref } from 'vue';

export class GisAssets {
  static loaded = ref(false);

  static async inject() {
    // 缓存key说明: 线上环境使用版本作为key， 本地开发环境每5分钟换一次key
    // const t = window.$SYS_CFG.version.main || (new Date().getTime() / 300e3) >> 0;

    // await window.$_ScriptInjector.inject(window.$SYS_CFG.gisPkgCss + '&t=' + t, {
    //   type: 'css',
    //   isLink: true,
    // });
    // await window.$_ScriptInjector.inject(window.$SYS_CFG.gisPkgJs + '&t=' + t, {
    //   type: 'js',
    //   isLink: true,
    // });

    // 初始化IndoorMap,IndoorThree
    IndoorMap.init();
    IndoorThree.init();

    // 自定义巡检路径样式
    IndoorThree.CONST_Color_InspectionLineColor = 0x33cc61;
    IndoorThree.CONST_Color_InspectionLineArrowColor = '#fff';

    IndoorThree.initInspectionPlugin();

    this.loaded.value = true;
  }
}
