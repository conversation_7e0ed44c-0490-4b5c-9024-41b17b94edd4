import vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers';
import { resolve } from 'path';
import { envConfigPlugin, manifestPlugin } from '@tanzerfe/plugins-vite';

export default () => {
  return {
    base: './',
    server: {
      open: true,
      port: 8382,
      hmr: true,
    },
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api'],
          additionalData: `@import "src/css/functions.scss";\n@import "src/css/variables.scss";`,
          api: 'modern-compiler',
        },
      },
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        src: resolve(__dirname, 'src'),
      },
    },
    build: {
      outDir: 'ehs-inspect',
      manifest: true,
    },
    plugins: [
      vue(),
      Components({
        resolvers: [NaiveUiResolver()],
        directoryAsNamespace: true,
      }),
      manifestPlugin({
        output: 'ehs-inspect',
        preload: ['com/lib.js', 'com/config.js'],
        exclude: [],
        enableLog: false,
      }),
      envConfigPlugin({
        outputDir: 'ehs-inspect/com',
        obfuscate: true,
      }),
    ],
  };
};
