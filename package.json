{"name": "ehs-inspect", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev --mode dev", "build:dev": "vite build --mode dev", "build:release": "vite build --mode release", "build:gcya": "vite build --mode gcya", "tsc": "vue-tsc --noEmit", "lint": "eslint . --ext .js,.ts,.vue --ignore-path .eslint<PERSON>ore", "lint:fix": "eslint . --ext .js,.ts,.vue --fix", "prepare": "husky install", "preview": "vite preview"}, "dependencies": {"@tanzerfe/ifm-child": "^1.0.5", "@vueuse/core": "^10.9.0", "artplayer": "5.1.0", "colord": "^2.9.3", "dayjs": "^1.11.11", "downloader": "^0.1.2", "easytimer.js": "^4.6.0", "echarts": "^5.4.3", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "flv.js": "1.6.2", "hel-iso": "^4.3.2", "hel-micro": "^4.9.10", "hls.js": "1.5.3", "html2canvas": "^1.4.1", "js-file-downloader": "^1.1.25", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "rxjs": "^7.8.1", "sysend": "^1.17.4", "vue": "^3.4.21", "vue-router": "^4.3.2"}, "devDependencies": {"@commitlint/cli": "17.8.1", "@commitlint/config-angular": "17.8.1", "@kalimahapps/vue-icons": "^1.4.1", "@tanzerfe/eslint-config-lint": "^0.0.7", "@tanzerfe/http": "^1.0.1", "@tanzerfe/plugins-vite": "^0.0.1", "@types/lodash-es": "^4.17.12", "@types/node": "^20.12.10", "@types/querystringify": "^2.0.2", "@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.19", "eslint": "8.57.0", "husky": "8.0.3", "lint-staged": "^15.2.2", "naive-ui": "^2.38.2", "postcss": "^8.4.38", "querystringify": "^2.2.0", "sass-embedded": "1.79.3", "svgson": "5.2.1", "tailwind-config": "^0.1.2", "tailwindcss": "^3.4.3", "typescript": "^5.5.4", "unplugin-vue-components": "^0.27.0", "vite": "^5.2.0", "vue-tsc": "^2.1.4"}, "husky": {"hooks": {"pre-commit": "lint-staged", "post-commit": "git update-index --again", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{vue,ts,tsx,js,jsx}": "eslint --ignore-path .eslint<PERSON>ore --fix"}}